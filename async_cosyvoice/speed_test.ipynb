{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## 测试效果\n", "\n", "- 测试代码: [speed_test.ipynb](speed_test.ipynb)\n", "- 测试环境: Intel i5-12400 CPU, 48GB RAM, 1x NVIDIA GeForce RTX 4070\n", "- 运行环境: Ubuntu 24.04.1 LTS, cuda 12.4, python 3.10.16\n", "- 测试说明: 单任务执行的数据（非并发测试）\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### 使用**注意事项**，需要将该文件移动到 CosyVoise 目录下，并安装 Ipython 模块运行"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 默认情况下"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Sliding Window Attention is enabled but not implemented for `sdpa`; unexpected results may be encountered.\n", "/opt/anaconda3/envs/cosyvoice/lib/python3.10/site-packages/diffusers/models/lora.py:393: FutureWarning: `LoRACompatibleLinear` is deprecated and will be removed in version 1.0.0. Use of `LoRACompatibleLinear` is deprecated. Please switch to PEFT backend by installing PEFT: `pip install peft`.\n", "  deprecate(\"LoRACompatibleLinear\", \"1.0.0\", deprecation_message)\n", "2025-02-26 14:47:33,166 INFO input frame rate=25\n", "/opt/anaconda3/envs/cosyvoice/lib/python3.10/site-packages/onnxruntime/capi/onnxruntime_inference_collection.py:115: UserWarning: Specified provider 'CUDAExecutionProvider' is not in available provider names.Available providers: 'AzureExecutionProvider, CPUExecutionProvider'\n", "  warnings.warn(\n", "/home/<USER>/github/FunAudioLLM-APP/cosyvoice/cosyvoice/cli/frontend.py:59: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n", "  self.spk2info = torch.load(spk2info, map_location=self.device)\n", "2025-02-26 14:47:34,392 WETEXT INFO found existing fst: /opt/anaconda3/envs/cosyvoice/lib/python3.10/site-packages/tn/zh_tn_tagger.fst\n", "2025-02-26 14:47:34,392 INFO found existing fst: /opt/anaconda3/envs/cosyvoice/lib/python3.10/site-packages/tn/zh_tn_tagger.fst\n", "2025-02-26 14:47:34,393 WETEXT INFO                     /opt/anaconda3/envs/cosyvoice/lib/python3.10/site-packages/tn/zh_tn_verbalizer.fst\n", "2025-02-26 14:47:34,393 INFO                     /opt/anaconda3/envs/cosyvoice/lib/python3.10/site-packages/tn/zh_tn_verbalizer.fst\n", "2025-02-26 14:47:34,393 WETEXT INFO skip building fst for zh_normalizer ...\n", "2025-02-26 14:47:34,393 INFO skip building fst for zh_normalizer ...\n", "2025-02-26 14:47:34,592 WETEXT INFO found existing fst: /opt/anaconda3/envs/cosyvoice/lib/python3.10/site-packages/tn/en_tn_tagger.fst\n", "2025-02-26 14:47:34,592 INFO found existing fst: /opt/anaconda3/envs/cosyvoice/lib/python3.10/site-packages/tn/en_tn_tagger.fst\n", "2025-02-26 14:47:34,593 WETEXT INFO                     /opt/anaconda3/envs/cosyvoice/lib/python3.10/site-packages/tn/en_tn_verbalizer.fst\n", "2025-02-26 14:47:34,593 INFO                     /opt/anaconda3/envs/cosyvoice/lib/python3.10/site-packages/tn/en_tn_verbalizer.fst\n", "2025-02-26 14:47:34,593 WETEXT INFO skip building fst for en_normalizer ...\n", "2025-02-26 14:47:34,593 INFO skip building fst for en_normalizer ...\n", "/home/<USER>/github/FunAudioLLM-APP/cosyvoice/cosyvoice/cli/model.py:70: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n", "  self.llm.load_state_dict(torch.load(llm_model, map_location=self.device), strict=True)\n", "/home/<USER>/github/FunAudioLLM-APP/cosyvoice/cosyvoice/cli/model.py:72: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n", "  self.flow.load_state_dict(torch.load(flow_model, map_location=self.device), strict=True)\n", "/home/<USER>/github/FunAudioLLM-APP/cosyvoice/cosyvoice/cli/model.py:75: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.\n", "  hift_state_dict = {k.replace('generator.', ''): v for k, v in torch.load(hift_model, map_location=self.device).items()}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[02/26/2025-14:47:37] [TRT] [I] Loaded engine size: 158 MiB\n", "[02/26/2025-14:47:37] [TRT] [I] [MS] Running engine with multi stream info\n", "[02/26/2025-14:47:37] [TRT] [I] [MS] Number of aux streams is 1\n", "[02/26/2025-14:47:37] [TRT] [I] [MS] Number of total worker streams is 2\n", "[02/26/2025-14:47:37] [TRT] [I] [MS] The main stream provided by execute/enqueue calls is the first worker stream\n", "[02/26/2025-14:47:37] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +0, GPU +4545, now: CPU 0, GPU 4681 (MiB)\n"]}], "source": ["import time\n", "import asyncio\n", "import torchaudio\n", "\n", "import sys\n", "sys.path.append('third_party/Matcha-TTS')\n", "\n", "from cosyvoice.cli.cosyvoice import  CosyVoice2\n", "from cosyvoice.utils.file_utils import load_wav\n", "\n", "prompt_text = '希望你以后能够做得比我还好哟'\n", "prompt_speech_16k = load_wav('./asset/zero_shot_prompt.wav', 16000)\n", "\n", "# cosyvoice = CosyVoice2('./pretrained_models/CosyVoice2-0.5B', load_jit=False, load_trt=False, fp16=True)\n", "cosyvoice = CosyVoice2('./pretrained_models/CosyVoice2-0.5B', load_jit=True, load_trt=True, fp16=True)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-02-26 14:50:17,298 INFO synthesis text 收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-02-26 14:50:20,558 INFO yield speech len 12.52, rtf 0.2603567636812838\n", "100%|██████████| 1/1 [00:03<00:00,  3.58s/it]\n"]}], "source": ["for i, j in enumerate(cosyvoice.inference_zero_shot('收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。', prompt_text, prompt_speech_16k, stream=False)):\n", "    torchaudio.save('zero_shot_{}.wav'.format(i), j['tts_speech'], cosyvoice.sample_rate)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]2025-02-26 14:50:25,638 INFO synthesis text 收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-02-26 14:50:26,355 INFO yield speech len 1.84, rtf 0.3896181998045548\n", "2025-02-26 14:50:26,884 INFO yield speech len 2.0, rtf 0.2637672424316406\n", "2025-02-26 14:50:27,409 INFO yield speech len 2.0, rtf 0.2617619037628174\n", "2025-02-26 14:50:27,960 INFO yield speech len 2.0, rtf 0.2750217914581299\n", "2025-02-26 14:50:28,433 INFO yield speech len 2.0, rtf 0.23574376106262207\n", "2025-02-26 14:50:28,850 INFO yield speech len 1.76, rtf 0.23584352298216385\n", "100%|██████████| 1/1 [00:03<00:00,  3.52s/it]\n"]}], "source": ["for i, j in enumerate(cosyvoice.inference_zero_shot('收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。', prompt_text, prompt_speech_16k, stream=True)):\n", "    torchaudio.save('zero_shot_{}.wav'.format(i), j['tts_speech'], cosyvoice.sample_rate)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-26 14:50:55,763 INFO get tts_text generator, will skip text_normalize!\n", "  0%|          | 0/1 [00:00<?, ?it/s]2025-02-26 14:50:55,768 INFO get tts_text generator, will return _extract_text_token_generator!\n", "2025-02-26 14:50:56,083 INFO synthesis text <generator object text_generator at 0x79821eaa2180>\n", "2025-02-26 14:50:56,086 INFO append 5 text token 15 speech token\n", "2025-02-26 14:50:56,086 INFO append 5 text token 15 speech token\n", "2025-02-26 14:50:56,087 INFO append 5 text token 15 speech token\n", "2025-02-26 14:50:56,087 INFO append 5 text token 15 speech token\n", "2025-02-26 14:50:56,087 INFO append 5 text token 15 speech token\n", "2025-02-26 14:50:56,088 INFO not enough text token to decode, wait for more\n", "2025-02-26 14:50:56,088 INFO append 5 text token 12 speech token\n", "2025-02-26 14:50:56,211 INFO fill_token index 3 next fill_token index 19\n", "2025-02-26 14:50:56,212 INFO get fill token, need to append more text token\n", "2025-02-26 14:50:56,213 INFO append 5 text token\n", "2025-02-26 14:50:56,389 INFO fill_token index 19 next fill_token index 35\n", "2025-02-26 14:50:56,390 INFO get fill token, need to append more text token\n", "2025-02-26 14:50:56,390 INFO append 5 text token\n", "2025-02-26 14:50:56,550 INFO fill_token index 35 next fill_token index 51\n", "2025-02-26 14:50:56,551 INFO no more text token, decode until met eos\n", "2025-02-26 14:50:59,270 INFO yield speech len 12.28, rtf 0.25946981355499366\n", "100%|██████████| 1/1 [00:03<00:00,  3.51s/it]\n"]}], "source": ["def text_generator():\n", "    yield '收到好友从远方寄来的生日礼物，'\n", "    yield '那份意外的惊喜与深深的祝福'\n", "    yield '让我心中充满了甜蜜的快乐，'\n", "    yield '笑容如花儿般绽放。'\n", "for i, j in enumerate(cosyvoice.inference_zero_shot(text_generator(), prompt_text, prompt_speech_16k, stream=False)):\n", "    torchaudio.save('zero_shot_{}.wav'.format(i), j['tts_speech'], cosyvoice.sample_rate)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-26 14:51:07,602 INFO get tts_text generator, will skip text_normalize!\n", "  0%|          | 0/1 [00:00<?, ?it/s]2025-02-26 14:51:07,607 INFO get tts_text generator, will return _extract_text_token_generator!\n", "2025-02-26 14:51:07,958 INFO synthesis text <generator object text_generator at 0x79821eaa2650>\n", "2025-02-26 14:51:07,959 INFO append 5 text token 15 speech token\n", "2025-02-26 14:51:07,960 INFO append 5 text token 15 speech token\n", "2025-02-26 14:51:07,960 INFO append 5 text token 15 speech token\n", "2025-02-26 14:51:07,961 INFO append 5 text token 15 speech token\n", "2025-02-26 14:51:07,961 INFO append 5 text token 15 speech token\n", "2025-02-26 14:51:07,961 INFO not enough text token to decode, wait for more\n", "2025-02-26 14:51:07,962 INFO append 5 text token 12 speech token\n", "2025-02-26 14:51:08,076 INFO fill_token index 3 next fill_token index 19\n", "2025-02-26 14:51:08,077 INFO get fill token, need to append more text token\n", "2025-02-26 14:51:08,078 INFO append 5 text token\n", "2025-02-26 14:51:08,365 INFO fill_token index 19 next fill_token index 35\n", "2025-02-26 14:51:08,365 INFO get fill token, need to append more text token\n", "2025-02-26 14:51:08,366 INFO append 5 text token\n", "2025-02-26 14:51:08,636 INFO fill_token index 35 next fill_token index 51\n", "2025-02-26 14:51:08,637 INFO no more text token, decode until met eos\n", "2025-02-26 14:51:08,945 INFO yield speech len 1.84, rtf 0.5363299794819044\n", "2025-02-26 14:51:09,452 INFO yield speech len 2.0, rtf 0.25229954719543457\n", "2025-02-26 14:51:10,075 INFO yield speech len 2.0, rtf 0.31104183197021484\n", "2025-02-26 14:51:10,725 INFO yield speech len 2.0, rtf 0.3238917589187622\n", "2025-02-26 14:51:11,197 INFO yield speech len 2.0, rtf 0.23549187183380127\n", "2025-02-26 14:51:11,772 INFO yield speech len 2.0, rtf 0.28693246841430664\n", "2025-02-26 14:51:11,971 INFO yield speech len 0.76, rtf 0.25982511670965897\n", "100%|██████████| 1/1 [00:04<00:00,  4.37s/it]\n"]}], "source": ["def text_generator():\n", "    yield '收到好友从远方寄来的生日礼物，'\n", "    yield '那份意外的惊喜与深深的祝福'\n", "    yield '让我心中充满了甜蜜的快乐，'\n", "    yield '笑容如花儿般绽放。'\n", "for i, j in enumerate(cosyvoice.inference_zero_shot(text_generator(), prompt_text, prompt_speech_16k, stream=True)):\n", "    torchaudio.save('zero_shot_{}.wav'.format(i), j['tts_speech'], cosyvoice.sample_rate)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]2025-02-26 14:51:27,736 INFO synthesis text 收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-02-26 14:51:30,704 INFO yield speech len 11.04, rtf 0.2689106092936751\n", "100%|██████████| 1/1 [00:03<00:00,  3.29s/it]\n"]}], "source": ["# instruct usage\n", "for i, j in enumerate(cosyvoice.inference_instruct2('收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。', '用四川话说这句话', prompt_speech_16k, stream=False)):\n", "    torchaudio.save('instruct2_{}.wav'.format(i), j['tts_speech'], cosyvoice.sample_rate)\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-02-26 14:51:58,201 INFO get tts_text generator, will skip text_normalize!\n", "  0%|          | 0/1 [00:00<?, ?it/s]2025-02-26 14:51:58,208 INFO get tts_text generator, will return _extract_text_token_generator!\n", "2025-02-26 14:51:58,536 INFO synthesis text <generator object text_generator at 0x79821eaa1ee0>\n", "2025-02-26 14:51:58,537 INFO get fill token, need to append more text token\n", "2025-02-26 14:51:58,538 INFO append 5 text token\n", "2025-02-26 14:51:58,793 INFO fill_token index 15 next fill_token index 31\n", "2025-02-26 14:51:58,793 INFO get fill token, need to append more text token\n", "2025-02-26 14:51:58,794 INFO append 5 text token\n", "2025-02-26 14:51:58,949 INFO fill_token index 31 next fill_token index 47\n", "2025-02-26 14:51:58,950 INFO get fill token, need to append more text token\n", "2025-02-26 14:51:58,950 INFO append 5 text token\n", "2025-02-26 14:51:59,106 INFO fill_token index 47 next fill_token index 63\n", "2025-02-26 14:51:59,107 INFO get fill token, need to append more text token\n", "2025-02-26 14:51:59,107 INFO append 5 text token\n", "2025-02-26 14:51:59,254 INFO fill_token index 63 next fill_token index 79\n", "2025-02-26 14:51:59,255 INFO no more text token, decode until met eos\n", "2025-02-26 14:52:02,039 INFO yield speech len 13.52, rtf 0.2591414564459987\n", "100%|██████████| 1/1 [00:03<00:00,  3.84s/it]\n"]}], "source": ["# instruct usage\n", "def text_generator():\n", "    yield '收到好友从远方寄来的生日礼物，'\n", "    yield '那份意外的惊喜与深深的祝福'\n", "    yield '让我心中充满了甜蜜的快乐，'\n", "    yield '笑容如花儿般绽放。'\n", "for i, j in enumerate(cosyvoice.inference_instruct2(text_generator(), '用四川话说这句话', prompt_speech_16k, stream=False)):\n", "    torchaudio.save('instruct2_{}.wav'.format(i), j['tts_speech'], cosyvoice.sample_rate)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 使用vllm加速llm推理"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "#### **注意：**\n", "\n", "在 jupyter notebook 中，如果要运行下列代码，需要将vllm_use_cosyvoice2_model.py正确复制到 vllm 包中，并注册到 _VLLM_MODELS 字典中。\n", "\n", "运行下面的 code 完成"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/anaconda3/envs/cosyvoice2/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "2025-02-26 15:21:27,119\tINFO util.py:154 -- Missing packages: ['ipywidgets']. Run `pip install -U ipywidgets`, then restart the notebook server for rich notebook output.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO 02-26 15:21:27 __init__.py:207] Automatically detected platform cuda.\n", "vllm package path: /opt/anaconda3/envs/cosyvoice2/lib/python3.10/site-packages/vllm\n", "Copied ./async_cosyvoice/vllm_use_cosyvoice2_model.py to /opt/anaconda3/envs/cosyvoice2/lib/python3.10/site-packages/vllm/model_executor/models/cosyvoice2.py\n", "Successfully updated registry.py\n", "All operations completed successfully!\n"]}], "source": ["import os\n", "import shutil\n", "\n", "# 获取vllm包的安装路径\n", "try:\n", "    import vllm\n", "except ImportError:\n", "    raise ImportError(\"vllm package not installed\")\n", "\n", "\n", "vllm_path = os.path.dirname(vllm.__file__)\n", "print(f\"vllm package path: {vllm_path}\")\n", "\n", "# 定义目标路径\n", "target_dir = os.path.join(vllm_path, \"model_executor\", \"models\")\n", "target_file = os.path.join(target_dir, \"cosyvoice2.py\")\n", "\n", "# 复制模型文件\n", "source_file = \"./async_cosyvoice/vllm_use_cosyvoice2_model.py\"\n", "if not os.path.exists(source_file):\n", "    raise FileNotFoundError(f\"Source file {source_file} not found\")\n", "\n", "shutil.copy(source_file, target_file)\n", "print(f\"Copied {source_file} to {target_file}\")\n", "\n", "# 修改registry.py文件\n", "registry_path = os.path.join(target_dir, \"registry.py\")\n", "new_entry = '    \"CosyVoice2Model\": (\"cosyvoice2\", \"CosyVoice2Model\"),  # noqa: E501\\n'\n", "\n", "# 读取并修改文件内容\n", "with open(registry_path, \"r\") as f:\n", "    lines = f.readlines()\n", "\n", "# 检查是否已存在条目\n", "entry_exists = any(\"CosyVoice2Model\" in line for line in lines)\n", "\n", "if not entry_exists:\n", "    # 寻找插入位置\n", "    insert_pos = None\n", "    for i, line in enumerate(lines):\n", "        if line.strip().startswith(\"**_FALLBACK_MODEL\"):\n", "            insert_pos = i + 1\n", "            break\n", "    \n", "    if insert_pos is None:\n", "        raise ValueError(\"Could not find insertion point in registry.py\")\n", "    \n", "    # 插入新条目\n", "    lines.insert(insert_pos, new_entry)\n", "    \n", "    # 写回文件\n", "    with open(registry_path, \"w\") as f:\n", "        f.writelines(lines)\n", "    print(\"Successfully updated registry.py\")\n", "else:\n", "    print(\"Entry already exists in registry.py, skipping modification\")\n", "\n", "print(\"All operations completed successfully!\")"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:26:15,640 - modelscope - INFO - PyTorch version 2.5.1 Found.\n", "2025-03-02 17:26:15,641 - modelscope - INFO - Loading ast index from /home/<USER>/.cache/modelscope/ast_indexer\n", "2025-03-02 17:26:15,659 - modelscope - INFO - Loading done! Current index file version is 1.15.0, with md5 fa842134a31dcdbdb54636891ec38df6 and a total number of 980 components indexed\n", "/opt/anaconda3/envs/cosyvoice2/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "2025-03-02 17:26:17,219\tINFO util.py:154 -- Missing packages: ['ipywidgets']. Run `pip install -U ipywidgets`, then restart the notebook server for rich notebook output.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO 03-02 17:26:17 __init__.py:207] Automatically detected platform cuda.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:26:18,085 INFO Loaded private_config.py\n"]}, {"name": "stdout", "output_type": "stream", "text": ["WARNING 03-02 17:26:18 registry.py:352] Model architecture CosyVoice2Model is already registered, and will be overwritten by the new model class <class 'async_cosyvoice.vllm_use_cosyvoice2_model.CosyVoice2Model'>.\n", "failed to import ttsfrd, use WeTextProcessing instead\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/anaconda3/envs/cosyvoice2/lib/python3.10/site-packages/diffusers/models/lora.py:393: FutureWarning: `LoRACompatibleLinear` is deprecated and will be removed in version 1.0.0. Use of `LoRACompatibleLinear` is deprecated. Please switch to PEFT backend by installing PEFT: `pip install peft`.\n", "  deprecate(\"LoRACompatibleLinear\", \"1.0.0\", deprecation_message)\n", "2025-03-02 17:26:21,015 INFO input frame rate=25\n", "\u001b[0;93m2025-03-02 17:26:21.906905918 [W:onnxruntime:, transformer_memcpy.cc:74 ApplyImpl] 8 Memcpy nodes are added to the graph main_graph for CUDAExecutionProvider. It might have negative impact on performance (including unable to run CUDA graph). Set session_options.log_severity_level=1 to see the detail logs before this message.\u001b[m\n", "\u001b[0;93m2025-03-02 17:26:21.909037523 [W:onnxruntime:, session_state.cc:1166 VerifyEachNodeIsAssignedToAnEp] Some nodes were not assigned to the preferred execution providers which may or may not have an negative impact on performance. e.g. ORT explicitly assigns shape related ops to CPU to improve perf.\u001b[m\n", "\u001b[0;93m2025-03-02 17:26:21.909044885 [W:onnxruntime:, session_state.cc:1168 VerifyEachNodeIsAssignedToAnEp] Rerunning with verbose output on a non-minimal build will show node assignments.\u001b[m\n", "2025-03-02 17:26:22,047 WETEXT INFO found existing fst: /opt/anaconda3/envs/cosyvoice2/lib/python3.10/site-packages/tn/zh_tn_tagger.fst\n", "2025-03-02 17:26:22,047 INFO found existing fst: /opt/anaconda3/envs/cosyvoice2/lib/python3.10/site-packages/tn/zh_tn_tagger.fst\n", "2025-03-02 17:26:22,047 WETEXT INFO                     /opt/anaconda3/envs/cosyvoice2/lib/python3.10/site-packages/tn/zh_tn_verbalizer.fst\n", "2025-03-02 17:26:22,047 INFO                     /opt/anaconda3/envs/cosyvoice2/lib/python3.10/site-packages/tn/zh_tn_verbalizer.fst\n", "2025-03-02 17:26:22,048 WETEXT INFO skip building fst for zh_normalizer ...\n", "2025-03-02 17:26:22,048 INFO skip building fst for zh_normalizer ...\n", "2025-03-02 17:26:22,239 WETEXT INFO found existing fst: /opt/anaconda3/envs/cosyvoice2/lib/python3.10/site-packages/tn/en_tn_tagger.fst\n", "2025-03-02 17:26:22,239 INFO found existing fst: /opt/anaconda3/envs/cosyvoice2/lib/python3.10/site-packages/tn/en_tn_tagger.fst\n", "2025-03-02 17:26:22,240 WETEXT INFO                     /opt/anaconda3/envs/cosyvoice2/lib/python3.10/site-packages/tn/en_tn_verbalizer.fst\n", "2025-03-02 17:26:22,240 INFO                     /opt/anaconda3/envs/cosyvoice2/lib/python3.10/site-packages/tn/en_tn_verbalizer.fst\n", "2025-03-02 17:26:22,240 WETEXT INFO skip building fst for en_normalizer ...\n", "2025-03-02 17:26:22,240 INFO skip building fst for en_normalizer ...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO 03-02 17:26:22 config.py:549] This model supports multiple tasks: {'embed', 'classify', 'generate', 'reward', 'score'}. Defaulting to 'generate'.\n", "INFO 03-02 17:26:22 config.py:1555] Chunked prefill is enabled with max_num_batched_tokens=1024.\n", "WARNING 03-02 17:26:23 utils.py:2128] CUDA was previously initialized. We must use the `spawn` multiprocessing start method. Setting VLLM_WORKER_MULTIPROC_METHOD to 'spawn'. See https://docs.vllm.ai/en/latest/getting_started/troubleshooting.html#python-multiprocessing for more information.\n", "INFO 03-02 17:26:25 __init__.py:207] Automatically detected platform cuda.\n", "INFO 03-02 17:26:26 core.py:50] Initializing a V1 LLM engine (v0.7.3) with config: model='./pretrained_models/CosyVoice2-0.5B', speculative_config=None, tokenizer='./pretrained_models/CosyVoice2-0.5B', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.bfloat16, max_seq_len=1024, download_dir=None, load_format=auto, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=None, enforce_eager=False, kv_cache_dtype=auto,  device_config=cuda, decoding_config=DecodingConfig(guided_decoding_backend='xgrammar'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=./pretrained_models/CosyVoice2-0.5B, num_scheduler_steps=1, multi_step_stream_outputs=True, enable_prefix_caching=True, chunked_prefill_enabled=True, use_async_output_proc=True, disable_mm_preprocessor_cache=False, mm_processor_kwargs=None, pooler_config=None, compilation_config={\"level\":3,\"custom_ops\":[\"none\"],\"splitting_ops\":[\"vllm.unified_attention\",\"vllm.unified_attention_with_output\"],\"use_inductor\":true,\"compile_sizes\":[],\"use_cudagraph\":true,\"cudagraph_num_of_warmups\":1,\"cudagraph_capture_sizes\":[512,504,496,488,480,472,464,456,448,440,432,424,416,408,400,392,384,376,368,360,352,344,336,328,320,312,304,296,288,280,272,264,256,248,240,232,224,216,208,200,192,184,176,168,160,152,144,136,128,120,112,104,96,88,80,72,64,56,48,40,32,24,16,8,4,2,1],\"max_capture_size\":512}\n", "WARNING 03-02 17:26:26 utils.py:2262] Methods determine_num_available_blocks,device_config,get_cache_block_size_bytes,list_loras,load_config,pin_lora,remove_lora,scheduler_config not implemented in <vllm.v1.worker.gpu_worker.Worker object at 0x7b974bf03fd0>\n", "INFO 03-02 17:26:26 gpu_model_runner.py:1049] Starting to load model ./pretrained_models/CosyVoice2-0.5B...\n", "INFO 03-02 17:26:26 cuda.py:157] Using Flash Attention backend on V1 engine.\n", "INFO 03-02 17:26:26 topk_topp_sampler.py:36] Using FlashInfer for top-p & top-k sampling.\n", "INFO 03-02 17:26:26 rejection_sampler.py:37] Using FlashInfer for rejection sampling.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/opt/anaconda3/envs/cosyvoice2/lib/python3.10/site-packages/torch/utils/_device.py:106: UserWarning: To copy construct from a tensor, it is recommended to use sourceTensor.clone().detach() or sourceTensor.clone().detach().requires_grad_(True), rather than torch.tensor(sourceTensor).\n", "  return func(*args, **kwargs)\n", "Loading pt checkpoint shards:   0% Completed | 0/1 [00:00<?, ?it/s]\n", "Loading pt checkpoint shards: 100% Completed | 1/1 [00:00<00:00,  1.11it/s]\n", "Loading pt checkpoint shards: 100% Completed | 1/1 [00:00<00:00,  1.11it/s]\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO 03-02 17:26:27 gpu_model_runner.py:1060] Loading model weights took 0.9532 GB\n", "INFO 03-02 17:26:31 backends.py:408] Using cache directory: /home/<USER>/.cache/vllm/torch_compile_cache/f48b0f0dcc/rank_0 for vLLM's torch.compile\n", "INFO 03-02 17:26:31 backends.py:418] Dynamo bytecode transform time: 3.60 s\n", "INFO 03-02 17:26:31 backends.py:115] Directly load the compiled graph for shape None from the cache\n", "INFO 03-02 17:26:34 monitor.py:33] torch.compile takes 3.60 s in total\n", "INFO 03-02 17:26:34 kv_cache_utils.py:522] # GPU blocks: 6873\n", "INFO 03-02 17:26:34 kv_cache_utils.py:525] Maximum concurrency for 1024 tokens per request: 107.39x\n", "INFO 03-02 17:26:44 gpu_model_runner.py:1339] Graph capturing finished in 11 secs, took 0.43 GiB\n", "INFO 03-02 17:26:44 core.py:116] init engine (profile, create kv cache, warmup model) took 17.01 seconds\n", "[03/02/2025-17:26:45] [TRT] [I] Loaded engine size: 158 MiB\n", "[03/02/2025-17:26:45] [TRT] [I] [MS] Running engine with multi stream info\n", "[03/02/2025-17:26:45] [TRT] [I] [MS] Number of aux streams is 1\n", "[03/02/2025-17:26:45] [TRT] [I] [MS] Number of total worker streams is 2\n", "[03/02/2025-17:26:45] [TRT] [I] [MS] The main stream provided by execute/enqueue calls is the first worker stream\n", "[03/02/2025-17:26:46] [TRT] [I] [MemUsageChange] TensorRT-managed allocation in IExecutionContext creation: CPU +0, GPU +4545, now: CPU 0, GPU 4681 (MiB)\n"]}], "source": ["import time\n", "import asyncio\n", "import torch\n", "import torchaudio\n", "\n", "import sys\n", "sys.path.append('third_party/Matcha-TTS')\n", "\n", "from async_cosyvoice.async_cosyvoice import AsyncCosyVoice2\n", "from cosyvoice.utils.file_utils import load_wav\n", "\n", "prompt_text = '希望你以后能够做得比我还好哟'\n", "prompt_speech_16k = load_wav('./asset/zero_shot_prompt.wav', 16000)\n", "\n", "# cosyvoice = AsyncCosyVoice2('./pretrained_models/CosyVoice2-0.5B', load_jit=False, load_trt=False, fp16=True)\n", "cosyvoice = AsyncCosyVoice2('./pretrained_models/CosyVoice2-0.5B', load_jit=True, load_trt=True, fp16=True)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]2025-03-02 17:27:12,400 INFO synthesis text 收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:27:13,482 INFO llm job done, time cost: 1.080s\n", "2025-03-02 17:27:13,483 DEBUG speech_tokens: len: 285  data: [3689, 4299, 4299, 4299, 4299, 4299, 4299, 3891, 5940, 6048, 3132, 4844, 5094, 543, 4700, 4862, 2687, 4860, 4860, 1223, 575, 4852, 5100, 3158, 707, 631, 4921, 567, 584, 4718, 5560, 3399, 221, 59, 20, 2073, 6534, 5591, 4955, 4473, 4236, 5405, 4677, 5020, 71, 3799, 731, 2921, 1110, 73, 32, 5319, 5322, 2315, 4798, 6013, 6042, 6070, 3800, 3798, 80, 566, 1781, 2092, 4345, 5830, 3644, 1454, 3570, 4218, 4218, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 4218, 3648, 3648, 5835, 3648, 1707, 1725, 5850, 4490, 386, 4347, 4239, 2243, 44, 4967, 4902, 4992, 4858, 1454, 1446, 5050, 5188, 142, 1026, 59, 879, 5651, 2499, 5075, 6424, 5532, 4440, 5483, 5405, 321, 3482, 566, 1843, 3004, 1546, 1789, 4092, 6316, 1700, 1616, 6021, 5322, 4593, 2264, 4499, 4561, 5226, 5319, 2406, 4487, 5205, 1281, 2243, 877, 1518, 6048, 6553, 3644, 1376, 6534, 6534, 2184, 2186, 728, 5090, 1299, 3645, 3645, 1701, 2031, 5940, 3072, 4537, 4874, 3589, 1430, 380, 2231, 5483, 4678, 2726, 4967, 6018, 5892, 4123, 5075, 5044, 4071, 5967, 4077, 2682, 5038, 3589, 6501, 6498, 3485, 3647, 857, 1775, 2189, 1923, 2244, 4380, 4866, 537, 305, 35, 1638, 2130, 5290, 4992, 798, 64, 389, 1383, 651, 2838, 5355, 5185, 8, 305, 3799, 3799, 74, 65, 973, 2028, 2112, 4299, 4299, 4299, 4299, 4299, 4299, 4218, 4299, 4218, 4218, 4218, 4299, 3651, 5136, 4753, 4676, 2553, 4565, 4946, 2813, 1937, 3798, 1937, 623, 593, 5773, 1392, 3888, 5832, 5859, 1458, 2112, 3969, 1854, 4286, 2912, 5102, 4947, 5028, 5028, 5057, 5671, 5429, 6157, 4052, 1649, 1917, 2166, 326, 2195, 3669, 1887, 5163, 3132, 4567, 2921, 767, 1480, 6429, 6537, 6534, 569, 1949, 1216, 2109, 4218, 4299, 4299, 4299, 4299, 4218, 4218]\n", "2025-03-02 17:27:13,667 INFO yield speech len 11.4, rtf 0.11113511888604415\n", "100%|██████████| 1/1 [00:01<00:00,  1.27s/it]\n"]}], "source": ["i = 0\n", "async for j in cosyvoice.inference_sft('收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。', spk_id='xiaohe', stream=False):\n", "    torchaudio.save('sft_{}.wav'.format(i), j['tts_speech'], cosyvoice.sample_rate)\n", "    i += 1\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]2025-03-02 17:27:26,755 INFO synthesis text 收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:27:27,786 INFO llm job done, time cost: 1.030s\n", "2025-03-02 17:27:27,787 DEBUG speech_tokens: len: 300  data: [3894, 5919, 5217, 5217, 5190, 3003, 2922, 2922, 1870, 5943, 5322, 157, 2891, 2585, 297, 3647, 3161, 2682, 5346, 4142, 1220, 599, 4852, 2832, 4939, 113, 515, 545, 5090, 1299, 2997, 1461, 2112, 6379, 2187, 605, 5095, 5289, 5046, 2555, 815, 788, 1801, 4353, 596, 4700, 4545, 6018, 5651, 2472, 5073, 4418, 3799, 734, 3650, 2276, 150, 1029, 4379, 3890, 974, 2, 4607, 5217, 6051, 2391, 2405, 5204, 5287, 5982, 5992, 2327, 3151, 3718, 2672, 1295, 1538, 2021, 6558, 5826, 3644, 728, 2096, 2032, 4299, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 4299, 2130, 3664, 5834, 5834, 2, 4350, 6453, 2243, 2195, 35, 68, 4427, 1978, 1960, 5073, 5668, 3320, 1456, 5093, 5833, 2924, 870, 59, 152, 3462, 5405, 2535, 4803, 5529, 5532, 5242, 6458, 5404, 3481, 2024, 1295, 1052, 4778, 1167, 3651, 5835, 5106, 1464, 1987, 6478, 6316, 3320, 1781, 3320, 5980, 6052, 204, 4490, 4561, 6051, 2316, 4521, 3909, 4403, 148, 2976, 3870, 5094, 5088, 5831, 4288, 6534, 4359, 2186, 1457, 1457, 5097, 1410, 4131, 2916, 3645, 1461, 1789, 1618, 3076, 5915, 2684, 2132, 626, 881, 5407, 4679, 376, 5453, 6178, 1887, 6051, 5339, 2819, 5099, 5070, 1299, 3645, 5835, 1869, 1518, 6051, 3078, 4878, 5089, 5777, 6501, 3582, 6077, 740, 1532, 734, 148, 1758, 4785, 538, 278, 5, 4068, 4068, 5073, 5020, 786, 113, 387, 648, 2754, 4950, 4536, 5269, 2195, 134, 3799, 2246, 308, 1757, 2032, 4299, 6486, 6486, 6486, 6486, 6486, 2031, 5136, 5405, 4677, 4543, 4942, 2795, 1208, 3883, 3884, 461, 2822, 2915, 5043, 3585, 1947, 5103, 3645, 1458, 1785, 1612, 1616, 722, 728, 5831, 5101, 4947, 4947, 4872, 5428, 5914, 5995, 5995, 1652, 1682, 3884, 2139, 2163, 4457, 5186, 4460, 4496, 5152, 5199, 5523, 6486, 4299, 3219, 4515, 4607, 5918, 734, 776, 1724, 6456, 6534, 2783, 6158, 488, 1262, 1948, 4299, 6486, 4299]\n", "2025-03-02 17:27:27,982 INFO yield speech len 12.0, rtf 0.10225355625152588\n", "100%|██████████| 1/1 [00:01<00:00,  1.38s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务完成，生成 1 个片段\n"]}], "source": ["task_id = 0\n", "\n", "tts_text = '收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。'\n", "\n", "chunk_num = 0\n", "audio_data: torch.Tensor = None\n", "async for chunk in cosyvoice.inference_zero_shot(tts_text, prompt_text, prompt_speech_16k, stream=False):\n", "    audio_data = torch.concat([audio_data, chunk['tts_speech']], dim=1) if audio_data is not None else chunk['tts_speech']\n", "    chunk_num += 1\n", "torchaudio.save('zero_shot_{}.wav'.format(task_id), audio_data, cosyvoice.sample_rate)\n", "print(f'任务完成，生成 {chunk_num} 个片段')\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:27:34,641 INFO synthesis text 收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:27:34,933 INFO yield speech len 0.44, rtf 0.6630626591769132\n", "2025-03-02 17:27:34,933 DEBUG multiples: 1.49, peer_chunk_token_num: 15\n", "2025-03-02 17:27:35,041 INFO yield speech len 0.6, rtf 0.179900328318278\n", "2025-03-02 17:27:35,042 DEBUG multiples: 4.87, peer_chunk_token_num: 30\n", "2025-03-02 17:27:35,177 INFO yield speech len 1.2, rtf 0.11265854040781657\n", "2025-03-02 17:27:35,177 DEBUG multiples: 11.84, peer_chunk_token_num: 45\n", "2025-03-02 17:27:35,425 INFO yield speech len 1.8, rtf 0.13787269592285156\n", "2025-03-02 17:27:35,438 DEBUG multiples: 18.52, peer_chunk_token_num: 60\n", "2025-03-02 17:27:35,710 INFO yield speech len 2.4, rtf 0.11330773433049521\n", "2025-03-02 17:27:35,722 DEBUG multiples: 27.03, peer_chunk_token_num: 75\n", "2025-03-02 17:27:36,077 INFO yield speech len 3.0, rtf 0.11838348706563313\n", "2025-03-02 17:27:36,081 DEBUG multiples: 35.48, peer_chunk_token_num: 90\n", "2025-03-02 17:27:36,342 INFO llm job done, time cost: 1.700s\n", "2025-03-02 17:27:36,342 DEBUG speech_tokens: len: 301  data: [3891, 5832, 5838, 3651, 5838, 5109, 5109, 3975, 4299, 4299, 3648, 6051, 2325, 2417, 4979, 144, 63, 5189, 3413, 4860, 5589, 4141, 494, 4973, 4603, 3562, 5587, 2297, 602, 599, 5054, 678, 5346, 5832, 3648, 2112, 6378, 5644, 495, 584, 5041, 5236, 4047, 5345, 2273, 5, 74, 3991, 4269, 3627, 5027, 4950, 4716, 2067, 5408, 4992, 4857, 2333, 47, 3650, 3650, 4517, 1029, 4433, 2192, 5105, 4466, 5146, 5319, 2406, 4604, 5302, 6043, 6045, 5334, 5333, 3881, 1504, 647, 539, 1133, 2090, 1457, 5100, 5100, 5101, 1439, 2059, 6486, 6486, 6486, 4299, 3894, 5835, 5838, 2922, 1464, 2031, 1884, 1477, 35, 2807, 3792, 6534, 38, 5, 4724, 2006, 1259, 4992, 5667, 3320, 2152, 2513, 5192, 2249, 1842, 65, 151, 6378, 5415, 3563, 4831, 5206, 4558, 4426, 5483, 5648, 1050, 2024, 1295, 5020, 2670, 3975, 5835, 2922, 1951, 6478, 4130, 1619, 5967, 4590, 156, 4526, 4557, 5142, 5319, 2316, 4476, 6180, 2224, 2416, 1761, 6048, 6310, 5094, 5088, 5092, 3644, 6453, 6535, 4359, 2186, 2915, 2813, 2006, 1947, 1782, 3648, 5835, 5109, 5838, 3003, 817, 4218, 3966, 2348, 3650, 2684, 2780, 4319, 683, 785, 2257, 6458, 4686, 5669, 4751, 6019, 1887, 5244, 3158, 2783, 5090, 5071, 1947, 2916, 5832, 2922, 2112, 2112, 4434, 3108, 972, 2692, 5018, 5777, 6501, 3583, 6158, 2918, 3719, 4043, 272, 3890, 5105, 2415, 2004, 4704, 2688, 323, 71, 3102, 4071, 5046, 5019, 1923, 2252, 4519, 2112, 648, 2754, 6084, 5268, 5271, 4571, 5986, 2234, 2249, 4697, 5645, 4929, 4850, 5834, 4862, 2657, 1696, 3883, 920, 488, 4946, 5053, 1979, 2031, 1945, 3159, 3645, 3648, 1545, 4299, 1744, 1617, 1616, 2102, 728, 2914, 5028, 5028, 2887, 5429, 5914, 5914, 3809, 1676, 6059, 1440, 1431, 2999, 4540, 6018, 1887, 4515, 4587, 4536, 4455, 2234, 3908, 6453, 6534, 2864, 6077, 4133, 2684, 1975, 4299, 6486, 6486, 6486, 6486, 6486, 4299]\n", "2025-03-02 17:27:36,593 INFO yield speech len 2.6, rtf 0.19698913280780497\n", "100%|██████████| 1/1 [00:02<00:00,  2.08s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["任务完成，生成 7 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["task_id = 0\n", "\n", "tts_text = '收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。'\n", "\n", "chunk_num = 0\n", "audio_data: torch.Tensor = None\n", "async for chunk in cosyvoice.inference_zero_shot(tts_text, prompt_text, prompt_speech_16k, stream=True):\n", "    audio_data = torch.concat([audio_data, chunk['tts_speech']], dim=1) if audio_data is not None else chunk['tts_speech']\n", "    chunk_num += 1\n", "torchaudio.save('zero_shot_{}.wav'.format(task_id), audio_data, cosyvoice.sample_rate)\n", "print(f'任务完成，生成 {chunk_num} 个片段')\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:28:47,999 INFO get tts_text generator, will skip text_normalize!\n", "  0%|          | 0/1 [00:00<?, ?it/s]2025-03-02 17:28:48,004 INFO get tts_text generator, will return _extract_text_token_generator!\n", "2025-03-02 17:28:48,178 INFO synthesis text <generator object text_generator at 0x7221c16a5af0>\n", "2025-03-02 17:28:48,179 INFO not enough text token to decode, wait for more\n", "2025-03-02 17:28:48,233 INFO get fill token, need to append more text token\n", "2025-03-02 17:28:48,235 INFO append 5 text token\n", "2025-03-02 17:28:48,356 INFO get fill token, need to append more text token\n", "2025-03-02 17:28:48,356 INFO append 5 text token\n", "2025-03-02 17:28:48,412 INFO no more text token, decode until met eos\n", "2025-03-02 17:28:49,427 INFO llm job done, time cost: 1.249s\n", "2025-03-02 17:28:49,428 DEBUG speech_tokens: len: 324  data: [4137, 5838, 5838, 5190, 3003, 3003, 816, 3975, 5217, 4512, 4758, 404, 353, 2747, 948, 64, 3650, 1217, 3411, 5589, 1955, 1217, 515, 2576, 2671, 3561, 2654, 2540, 2705, 4973, 1326, 3240, 2919, 6405, 6376, 6373, 2439, 599, 1457, 4286, 3400, 6316, 221, 734, 785, 1846, 4266, 2765, 5591, 4628, 3102, 4191, 5404, 5830, 4526, 235, 803, 1463, 3653, 4598, 2001, 4487, 5834, 2918, 1731, 5835, 3003, 2032, 5298, 5322, 4756, 2256, 4595, 4745, 5365, 6043, 6046, 5332, 5341, 3718, 3880, 76, 2834, 539, 1052, 2099, 3616, 5070, 5091, 5101, 2096, 4299, 4299, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 4299, 3894, 5838, 5109, 2922, 1788, 4299, 1560, 2231, 359, 368, 2321, 6534, 1332, 29, 287, 3455, 5640, 5020, 4697, 1451, 2914, 2513, 5837, 2303, 867, 143, 151, 4191, 5647, 3563, 4805, 5533, 4479, 5482, 6374, 3228, 3482, 638, 4517, 843, 5106, 2922, 1546, 1825, 4292, 1943, 3806, 5319, 4752, 156, 4598, 4552, 5160, 2322, 4579, 3747, 2935, 4564, 948, 5970, 4095, 5094, 5079, 5092, 3563, 6537, 6537, 2185, 2186, 638, 5054, 2683, 3402, 4299, 1947, 5835, 5838, 3003, 2113, 5919, 3309, 2582, 5840, 2441, 590, 1430, 728, 56, 2267, 6458, 5648, 3481, 5021, 6019, 4072, 4434, 3155, 5078, 5080, 5089, 1299, 5346, 1701, 2112, 3678, 5322, 3105, 2754, 2779, 2915, 6505, 6505, 6501, 2854, 1703, 1469, 737, 1532, 3800, 731, 6157, 2511, 3888, 5832, 735, 4299, 1032, 5460, 4947, 510, 566, 305, 8, 32, 1638, 6501, 6016, 5073, 5019, 1680, 58, 4564, 1302, 651, 2757, 6084, 5265, 5268, 4786, 2339, 5989, 5986, 2243, 4997, 4862, 1973, 2029, 6486, 4299, 3888, 5916, 5109, 5109, 817, 3165, 5161, 5648, 4920, 4769, 3647, 2675, 596, 947, 3886, 3884, 434, 599, 2786, 5053, 2793, 3402, 5589, 1944, 1821, 3799, 1937, 728, 5102, 5074, 5028, 5028, 2887, 4946, 5348, 5185, 4536, 4779, 4536, 5267, 1649, 1652, 3863, 1431, 2163, 2351, 5834, 5915, 4456, 1520, 6486, 6486, 6486, 6486, 6486, 4299, 4299, 3648, 5838, 5835, 2922, 1545, 4299, 4299]\n", "2025-03-02 17:28:49,643 INFO yield speech len 12.96, rtf 0.11309328270547184\n", "100%|██████████| 1/1 [00:01<00:00,  1.64s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["任务完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["task_id = 0\n", "\n", "# text_generator 字符数较多的时，（bfloat16下）llm的稳定性降低，语音会错乱，建议手动切分，直接传入少量文本流式推理\n", "\n", "def text_generator():\n", "    yield '收到好友从远方寄来的生日礼物，'\n", "    yield '那份意外的惊喜与深深的祝福'\n", "    yield '让我心中充满了甜蜜的快乐，'\n", "    yield '笑容如花儿般绽放。'\n", "\n", "tts_text = text_generator()\n", "\n", "chunk_num = 0\n", "audio_data: torch.Tensor = None\n", "async for chunk in cosyvoice.inference_zero_shot(tts_text, prompt_text, prompt_speech_16k, stream=False):\n", "    audio_data = torch.concat([audio_data, chunk['tts_speech']], dim=1) if audio_data is not None else chunk['tts_speech']\n", "    chunk_num += 1\n", "torchaudio.save('zero_shot_{}.wav'.format(task_id), audio_data, cosyvoice.sample_rate)\n", "print(f'任务完成，生成 {chunk_num} 个片段')\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:28:49,654 INFO get tts_text generator, will skip text_normalize!\n", "  0%|          | 0/1 [00:00<?, ?it/s]2025-03-02 17:28:49,656 INFO get tts_text generator, will return _extract_text_token_generator!\n", "2025-03-02 17:28:49,720 INFO synthesis text <generator object text_generator at 0x7221c16a5bd0>\n", "2025-03-02 17:28:49,721 INFO not enough text token to decode, wait for more\n", "2025-03-02 17:28:49,737 INFO get fill token, need to append more text token\n", "2025-03-02 17:28:49,737 INFO append 5 text token\n", "2025-03-02 17:28:49,861 INFO get fill token, need to append more text token\n", "2025-03-02 17:28:49,862 INFO append 5 text token\n", "2025-03-02 17:28:50,007 INFO yield speech len 0.44, rtf 0.6529071114280007\n", "2025-03-02 17:28:50,008 DEBUG multiples: 1.54, peer_chunk_token_num: 15\n", "2025-03-02 17:28:50,018 INFO no more text token, decode until met eos\n", "2025-03-02 17:28:50,107 INFO yield speech len 0.6, rtf 0.16451398531595868\n", "2025-03-02 17:28:50,107 DEBUG multiples: 5.15, peer_chunk_token_num: 30\n", "2025-03-02 17:28:50,254 INFO yield speech len 1.2, rtf 0.12226442495981853\n", "2025-03-02 17:28:50,255 DEBUG multiples: 11.91, peer_chunk_token_num: 45\n", "2025-03-02 17:28:50,485 INFO yield speech len 1.8, rtf 0.12783898247612846\n", "2025-03-02 17:28:50,498 DEBUG multiples: 19.13, peer_chunk_token_num: 60\n", "2025-03-02 17:28:50,769 INFO yield speech len 2.4, rtf 0.11295974254608154\n", "2025-03-02 17:28:50,770 DEBUG multiples: 28.05, peer_chunk_token_num: 75\n", "2025-03-02 17:28:51,148 INFO yield speech len 3.0, rtf 0.12588127454121908\n", "2025-03-02 17:28:51,149 DEBUG multiples: 35.83, peer_chunk_token_num: 90\n", "2025-03-02 17:28:51,673 INFO yield speech len 3.6, rtf 0.14573011133405897\n", "2025-03-02 17:28:51,674 DEBUG multiples: 41.58, peer_chunk_token_num: 105\n", "2025-03-02 17:28:51,686 INFO llm job done, time cost: 1.965s\n", "2025-03-02 17:28:51,686 DEBUG speech_tokens: len: 357  data: [1788, 1788, 5835, 5835, 5859, 5859, 3645, 3645, 3726, 3972, 5835, 5832, 3648, 2112, 3729, 6021, 3051, 2657, 5050, 387, 4402, 6077, 5599, 6318, 6318, 1220, 2789, 5086, 4614, 4535, 110, 491, 599, 4299, 2004, 4915, 2439, 635, 1430, 6502, 5560, 6316, 221, 734, 785, 1532, 4197, 5724, 5591, 2773, 5769, 6261, 6379, 4686, 5749, 4517, 3800, 3650, 3653, 138, 297, 2300, 5319, 2403, 4574, 6418, 5314, 6042, 6061, 3146, 1614, 404, 566, 1376, 1613, 2102, 4344, 6528, 5097, 5102, 2915, 1358, 4300, 4299, 4299, 1945, 5346, 5832, 5832, 5832, 3645, 1458, 2112, 1887, 5122, 5266, 5590, 2783, 6534, 6453, 4853, 4430, 4511, 4520, 4412, 2234, 1731, 5835, 3648, 1545, 4299, 5352, 5640, 4992, 4992, 4912, 4912, 2726, 2726, 1295, 4043, 4368, 5071, 5023, 5266, 5914, 5108, 3005, 2582, 2348, 1842, 56, 2249, 2166, 5650, 4929, 5668, 4803, 6259, 5772, 4803, 5164, 5482, 5408, 1293, 1295, 1295, 1295, 5021, 231, 735, 5835, 1464, 2031, 1294, 1295, 1943, 3887, 3712, 6048, 5241, 2339, 4490, 4481, 5290, 5236, 6048, 957, 4526, 3018, 1806, 2243, 220, 1518, 6048, 6553, 5827, 5828, 3641, 4373, 6537, 6537, 2172, 1457, 4373, 1454, 2915, 5089, 1410, 3159, 3645, 3729, 1869, 5916, 3864, 143, 5834, 497, 1322, 1430, 719, 110, 2267, 5482, 5648, 4677, 3563, 6182, 6182, 6262, 1833, 5322, 3881, 5015, 5018, 5075, 5776, 5775, 2031, 5913, 1458, 4299, 1491, 6048, 1620, 2754, 2776, 5093, 5074, 6505, 6504, 6501, 6258, 1396, 3890, 1460, 1463, 1505, 3962, 2, 4433, 1680, 60, 4704, 2679, 1266, 566, 62, 5, 68, 1557, 3991, 6178, 6261, 3828, 4966, 4992, 4992, 4912, 3481, 2753, 1923, 63, 2216, 1368, 573, 648, 2838, 2841, 2850, 5092, 5022, 5428, 5185, 5192, 2195, 116, 395, 160, 4042, 803, 4520, 4772, 4682, 2738, 2675, 5591, 1217, 1973, 2113, 4299, 6486, 6486, 4299, 4299, 1947, 5832, 5832, 5835, 5109, 3651, 1788, 5217, 5729, 2508, 4543, 5428, 4946, 2894, 3152, 3882, 947, 2813, 2813, 2897, 5047, 5043, 2112, 3888, 6075, 3645, 1623, 3756, 3789, 1612, 2102, 2105, 3641, 5830, 2760, 5028, 5028, 3643, 5030, 5591, 6319, 6400, 6157, 6157, 6237, 6237, 5267, 1625, 1679, 3872, 1440, 2166, 461, 5837, 4376, 4457, 4496, 5230, 2112, 3648, 5835, 3648]\n", "2025-03-02 17:28:51,911 INFO yield speech len 1.24, rtf 0.19112998439419654\n", "100%|██████████| 1/1 [00:02<00:00,  2.26s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["任务完成，生成 8 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["task_id = 0\n", "\n", "def text_generator():\n", "    yield '收到好友从远方寄来的生日礼物，'\n", "    yield '那份意外的惊喜与深深的祝福'\n", "    yield '让我心中充满了甜蜜的快乐，'\n", "    yield '笑容如花儿般绽放。'\n", "\n", "tts_text = text_generator()\n", "\n", "chunk_num = 0\n", "audio_data: torch.Tensor = None\n", "async for chunk in cosyvoice.inference_zero_shot(tts_text, prompt_text, prompt_speech_16k, stream=True):\n", "    audio_data = torch.concat([audio_data, chunk['tts_speech']], dim=1) if audio_data is not None else chunk['tts_speech']\n", "    chunk_num += 1\n", "torchaudio.save('zero_shot_{}.wav'.format(task_id), audio_data, cosyvoice.sample_rate)\n", "print(f'任务完成，生成 {chunk_num} 个片段')\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:29:40,124 INFO synthesis text 收到好友从远方寄来的生日礼物[breath]，那份意外的惊喜与深深的祝福[breath]让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:29:41,343 INFO llm job done, time cost: 1.218s\n", "2025-03-02 17:29:41,343 DEBUG speech_tokens: len: 276  data: [1490, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 3891, 6454, 6458, 315, 59, 468, 549, 4700, 5348, 6075, 6075, 3898, 5594, 5056, 5258, 6316, 5345, 4843, 5081, 641, 3462, 2538, 2695, 1370, 4019, 4126, 2429, 2303, 4537, 2538, 6537, 4970, 4942, 4708, 6261, 5415, 5162, 511, 1043, 1694, 47, 5837, 3082, 66, 2654, 2254, 6535, 3222, 59, 38, 6094, 6177, 5392, 4487, 3879, 2267, 5020, 5100, 4611, 5829, 5802, 5830, 3563, 728, 626, 2003, 1978, 2028, 5832, 5832, 5835, 5835, 1458, 2112, 3744, 748, 3647, 3890, 4862, 6453, 6534, 380, 2255, 4966, 2725, 1294, 1295, 3158, 5826, 5097, 5267, 196, 57, 4769, 159, 3462, 5245, 1376, 620, 3509, 6263, 5236, 5320, 5245, 2445, 5091, 5092, 314, 2006, 2032, 4299, 1942, 4129, 6317, 5169, 6536, 289, 56, 6094, 6366, 6453, 4492, 180, 2290, 4596, 876, 5648, 396, 2102, 2186, 1457, 3644, 6534, 6540, 6534, 3639, 5099, 2915, 626, 1358, 2032, 2028, 3645, 3648, 5838, 3732, 3732, 3732, 4299, 6372, 1044, 737, 254, 3509, 2879, 4844, 4444, 5240, 2193, 620, 2941, 6178, 5655, 4446, 4970, 5093, 5041, 5667, 5481, 3411, 2825, 2127, 1882, 4463, 4567, 4447, 5185, 144, 3462, 4407, 4704, 2778, 4760, 2195, 4490, 912, 4317, 2888, 231, 78, 4859, 1893, 648, 2766, 5356, 3899, 3647, 1466, 251, 805, 5987, 2261, 4780, 4946, 629, 1975, 5832, 5835, 5838, 2922, 1788, 5946, 5242, 5407, 322, 1460, 1220, 611, 5015, 3468, 4690, 5077, 5696, 4235, 4179, 6391, 3638, 1456, 2841, 2841, 701, 1304, 1946, 1703, 3809, 5296, 6060, 2403, 2160, 4571, 4733, 4462, 4486, 2085, 5404, 4535, 251, 737, 8, 2300, 3223, 6534, 6534, 5024, 6320, 6077, 1235, 1994, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 6082]\n", "2025-03-02 17:29:41,531 INFO yield speech len 11.04, rtf 0.12746418731800024\n", "100%|██████████| 1/1 [00:01<00:00,  1.54s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["任务完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# instruct usage\n", "task_id = 0\n", "\n", "tts_text = '收到好友从远方寄来的生日礼物[breath]，那份意外的惊喜与深深的祝福[breath]让我心中充满了甜蜜的快乐，笑容如花儿般绽放。'\n", "\n", "chunk_num = 0\n", "audio_data: torch.Tensor = None\n", "async for chunk in cosyvoice.inference_instruct2(tts_text, '用四川话说这句话', prompt_speech_16k, stream=False):\n", "    audio_data = torch.concat([audio_data, chunk['tts_speech']], dim=1) if audio_data is not None else chunk['tts_speech']\n", "    chunk_num += 1\n", "torchaudio.save('instruct2_{}.wav'.format(task_id), audio_data, cosyvoice.sample_rate)\n", "print(f'任务完成，生成 {chunk_num} 个片段')"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]2025-03-02 17:29:41,700 INFO synthesis text 收到好友从远方寄来的生日礼物[breath]，那份意外的惊喜与深深的祝福[breath]让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:29:41,932 INFO yield speech len 0.44, rtf 0.5264325575395064\n", "2025-03-02 17:29:41,932 DEBUG multiples: 2.34, peer_chunk_token_num: 15\n", "2025-03-02 17:29:42,006 INFO yield speech len 0.6, rtf 0.12295842170715332\n", "2025-03-02 17:29:42,007 DEBUG multiples: 7.45, peer_chunk_token_num: 30\n", "2025-03-02 17:29:42,272 INFO yield speech len 1.2, rtf 0.22076229254404706\n", "2025-03-02 17:29:42,272 DEBUG multiples: 10.86, peer_chunk_token_num: 45\n", "2025-03-02 17:29:42,473 INFO yield speech len 1.8, rtf 0.11140717400444879\n", "2025-03-02 17:29:42,474 DEBUG multiples: 19.29, peer_chunk_token_num: 60\n", "2025-03-02 17:29:42,745 INFO yield speech len 2.4, rtf 0.11273751656214397\n", "2025-03-02 17:29:42,757 DEBUG multiples: 27.87, peer_chunk_token_num: 75\n", "2025-03-02 17:29:43,134 INFO yield speech len 3.0, rtf 0.12592514355977377\n", "2025-03-02 17:29:43,137 DEBUG multiples: 35.61, peer_chunk_token_num: 90\n", "2025-03-02 17:29:43,165 INFO llm job done, time cost: 1.464s\n", "2025-03-02 17:29:43,166 DEBUG speech_tokens: len: 270  data: [1571, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 2112, 5862, 6537, 6458, 280, 299, 225, 540, 4565, 6157, 4628, 6075, 6075, 6329, 4946, 5056, 5582, 5587, 4603, 5076, 2828, 1275, 5401, 2448, 2798, 2129, 4019, 3158, 467, 4460, 4806, 4266, 3618, 4943, 4942, 4716, 3993, 5408, 366, 1535, 3800, 3650, 3082, 66, 4859, 3712, 6535, 6372, 65, 47, 6175, 6390, 5500, 4592, 1602, 2348, 4940, 5021, 4614, 5094, 4344, 6532, 4373, 4373, 719, 626, 2087, 1947, 3645, 5835, 5919, 5919, 3732, 2112, 2049, 3663, 3647, 3890, 4943, 4239, 6540, 2503, 4403, 2246, 4913, 3427, 1997, 1293, 566, 1208, 3643, 5090, 5918, 2357, 870, 4532, 808, 5651, 2274, 1295, 1268, 3428, 5047, 4443, 5563, 5245, 2445, 5010, 2834, 4126, 6070, 4534, 6455, 6373, 767, 74, 6177, 6376, 2232, 4510, 4071, 4480, 4857, 1038, 5645, 1089, 2102, 2105, 2105, 2102, 5827, 6465, 6507, 6534, 4368, 6559, 725, 707, 2059, 3888, 5916, 5838, 5838, 5919, 3732, 1869, 6456, 3231, 2921, 1226, 3509, 2879, 5078, 4435, 5242, 2463, 539, 5938, 3471, 4689, 5000, 4985, 5772, 5647, 2754, 592, 1427, 4314, 1881, 5273, 4540, 3070, 4456, 147, 1032, 4461, 4947, 2698, 2573, 2195, 4382, 4068, 4315, 2645, 1212, 2266, 2425, 573, 2838, 2841, 3415, 3890, 1703, 1463, 278, 151, 5986, 3233, 5023, 5057, 683, 598, 4131, 5832, 5835, 5919, 3003, 3894, 5946, 5242, 4678, 287, 1460, 1946, 599, 5015, 3465, 4691, 5077, 5048, 3506, 4235, 3964, 4043, 3644, 2841, 2841, 1401, 719, 1952, 1946, 5996, 5299, 4575, 3132, 945, 4814, 4706, 4543, 138, 5649, 4450, 278, 737, 8, 119, 6453, 6537, 4943, 6320, 6077, 3170, 1964, 1948, 4299, 6486, 6486, 4299, 4299, 4299, 4299, 4299]\n", "2025-03-02 17:29:43,372 INFO yield speech len 1.36, rtf 0.17269008299883673\n", "100%|██████████| 1/1 [00:01<00:00,  1.80s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["任务完成，生成 7 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["# instruct 不能使用 Generater 模式传入text\n", "task_id = 0\n", "\n", "tts_text = '收到好友从远方寄来的生日礼物[breath]，那份意外的惊喜与深深的祝福[breath]让我心中充满了甜蜜的快乐，笑容如花儿般绽放。'\n", "\n", "chunk_num = 0\n", "audio_data: torch.Tensor = None\n", "async for chunk in cosyvoice.inference_instruct2(tts_text, '用四川话说这句话', prompt_speech_16k, stream=True):\n", "    audio_data = torch.concat([audio_data, chunk['tts_speech']], dim=1) if audio_data is not None else chunk['tts_speech']\n", "    chunk_num += 1\n", "torchaudio.save('instruct2_{}.wav'.format(task_id), audio_data, cosyvoice.sample_rate)\n", "print(f'任务完成，生成 {chunk_num} 个片段')"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]2025-03-02 17:30:12,451 INFO synthesis text 收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:30:13,721 INFO llm job done, time cost: 1.243s\n", "2025-03-02 17:30:13,721 DEBUG speech_tokens: len: 304  data: [2112, 4299, 4215, 6159, 6159, 5916, 6156, 5913, 1866, 4299, 6081, 4755, 4752, 4752, 370, 2894, 5102, 477, 297, 4780, 6401, 3404, 5589, 6318, 1223, 656, 5099, 5344, 6316, 4601, 2894, 644, 546, 2646, 589, 1403, 6419, 6208, 6316, 2411, 116, 1802, 4359, 1414, 3407, 4880, 5283, 1833, 5651, 4407, 4507, 5986, 11, 8, 148, 288, 2225, 5725, 5401, 4448, 4438, 6094, 5445, 5472, 4447, 2264, 3802, 76, 485, 1376, 1844, 2095, 2177, 6559, 6559, 2186, 2176, 4299, 6486, 6486, 6486, 4299, 3972, 6159, 6159, 5913, 5916, 5916, 5913, 4299, 4299, 4299, 4299, 4074, 2207, 5348, 2675, 6534, 3627, 2540, 2267, 4993, 4992, 4912, 5507, 5582, 5090, 3323, 4463, 150, 54, 2246, 804, 2085, 4922, 4407, 5075, 6505, 5559, 4453, 4510, 4513, 4513, 4513, 4434, 3967, 3239, 3239, 1295, 2006, 2032, 1978, 6316, 4130, 1538, 4512, 4672, 2484, 4448, 5123, 5125, 4671, 4752, 4446, 5853, 6093, 2495, 1044, 1329, 5565, 3393, 5099, 1373, 6534, 6535, 6535, 6510, 2185, 2186, 1457, 3644, 3643, 6486, 6486, 4299, 3888, 5832, 5835, 5835, 5832, 5835, 3651, 4299, 4218, 3690, 4403, 5429, 1403, 3590, 692, 767, 4593, 4515, 2508, 4994, 5938, 6262, 2499, 3069, 2867, 4967, 6255, 303, 4836, 2673, 4954, 5046, 5772, 6499, 4943, 734, 1480, 1477, 29, 1842, 2004, 4380, 2760, 530, 98, 1884, 4317, 5533, 4992, 1590, 73, 136, 2112, 654, 2838, 4872, 5509, 5270, 143, 3722, 3722, 5905, 2495, 2729, 542, 1252, 4296, 4299, 4299, 4299, 4299, 5832, 5916, 5838, 5919, 5919, 5919, 3732, 4299, 5352, 4432, 4676, 4679, 4498, 5914, 2759, 728, 2099, 4124, 1937, 713, 617, 2051, 4235, 6421, 2041, 1993, 1855, 4367, 5102, 2841, 4947, 4947, 5027, 5752, 5752, 6238, 4052, 1892, 1892, 1189, 2166, 704, 5105, 4379, 5856, 3994, 4191, 5401, 4406, 5108, 857, 1805, 1830, 6459, 6453, 2756, 5591, 2693, 1967, 2112, 6486, 6486, 4299, 4299, 4299, 3975, 4299, 4299]\n", "2025-03-02 17:30:13,925 INFO yield speech index:0, len 12.16, rtf 0.121,  cost 1.475s,  all cost time 1.475s\n", "100%|██████████| 1/1 [00:01<00:00,  1.48s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["任务完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["task_id = 0\n", "\n", "tts_text = '收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。'\n", "\n", "chunk_num = 0\n", "audio_data: torch.Tensor = None\n", "async for chunk in cosyvoice.inference_zero_shot_by_spk_id(tts_text, spk_id='xiaohe', stream=False):\n", "    audio_data = torch.concat([audio_data, chunk['tts_speech']], dim=1) if audio_data is not None else chunk['tts_speech']\n", "    chunk_num += 1\n", "torchaudio.save('zero_shot_{}.wav'.format(task_id), audio_data, cosyvoice.sample_rate)\n", "print(f'任务完成，生成 {chunk_num} 个片段')\n"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]2025-03-02 17:30:13,952 INFO synthesis text 收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:30:14,230 INFO yield speech index:0, len 0.44, rtf 0.632,  cost 0.278s,  all cost time 0.278s\n", "2025-03-02 17:30:14,231 DEBUG multiples: 1.65, peer_chunk_token_num: 15\n", "2025-03-02 17:30:14,332 INFO yield speech index:1, len 0.60, rtf 0.170,  cost 0.102s,  all cost time 0.381s\n", "2025-03-02 17:30:14,333 DEBUG multiples: 5.31, peer_chunk_token_num: 30\n", "2025-03-02 17:30:14,484 INFO yield speech index:2, len 1.20, rtf 0.126,  cost 0.151s,  all cost time 0.532s\n", "2025-03-02 17:30:14,484 DEBUG multiples: 12.01, peer_chunk_token_num: 45\n", "2025-03-02 17:30:14,692 INFO yield speech index:3, len 1.80, rtf 0.116,  cost 0.208s,  all cost time 0.741s\n", "2025-03-02 17:30:14,705 DEBUG multiples: 19.97, peer_chunk_token_num: 60\n", "2025-03-02 17:30:15,033 INFO yield speech index:4, len 2.40, rtf 0.137,  cost 0.328s,  all cost time 1.082s\n", "2025-03-02 17:30:15,042 DEBUG multiples: 26.79, peer_chunk_token_num: 75\n", "2025-03-02 17:30:15,355 INFO llm job done, time cost: 1.400s\n", "2025-03-02 17:30:15,356 DEBUG speech_tokens: len: 254  data: [4299, 4134, 6075, 5913, 5916, 5832, 3726, 3648, 2112, 5214, 5562, 5319, 2882, 5085, 1113, 2242, 5023, 5675, 2683, 5589, 3415, 1304, 2894, 5345, 4859, 461, 710, 1451, 2733, 5562, 589, 5774, 6288, 6315, 224, 839, 1802, 4353, 686, 4949, 4717, 3993, 4922, 4695, 4857, 2989, 734, 59, 2989, 2243, 5157, 4753, 2241, 4493, 4475, 5233, 5260, 4448, 3771, 80, 1457, 1862, 2093, 2186, 6559, 6532, 4346, 2186, 2177, 2031, 3403, 3888, 5832, 5916, 3729, 3729, 2112, 3666, 5931, 4375, 5428, 3277, 6537, 2872, 4490, 4913, 5641, 5668, 5344, 3643, 5051, 4460, 73, 2243, 885, 4922, 78, 4994, 5777, 5529, 4749, 4509, 4513, 4512, 321, 323, 1052, 2105, 2086, 1943, 4130, 3887, 3806, 1619, 1862, 2986, 5319, 4509, 2234, 4474, 5152, 4509, 4500, 4473, 5850, 2243, 802, 4191, 3873, 6553, 5827, 4373, 4288, 6507, 4359, 2186, 2186, 1457, 719, 2112, 3969, 3969, 3645, 1458, 1626, 1581, 6070, 5266, 4949, 674, 710, 806, 4512, 4677, 2726, 5452, 6259, 3228, 3150, 5090, 5048, 5526, 5406, 6291, 4956, 5044, 6501, 6498, 3242, 1460, 11, 1775, 272, 65, 2166, 33, 4866, 501, 305, 1642, 2128, 3829, 5100, 78, 67, 2657, 681, 651, 2838, 6084, 5269, 5918, 35, 3722, 6148, 4691, 299, 389, 2000, 2028, 2031, 3645, 5835, 5835, 5916, 3732, 1869, 5379, 4753, 4675, 4534, 5266, 4949, 707, 1937, 1685, 707, 674, 3590, 5777, 4016, 1855, 2183, 6560, 4983, 5028, 2769, 6401, 6401, 4052, 3809, 3863, 1449, 2160, 3809, 4378, 5124, 1887, 4918, 4522, 5914, 5, 1559, 1800, 6537, 685, 6404, 5591, 509, 1952, 2112, 2031, 4299, 4299]\n", "2025-03-02 17:30:15,396 INFO yield speech index:5, len 3.00, rtf 0.118,  cost 0.353s,  all cost time 1.444s\n", "2025-03-02 17:30:15,578 INFO yield speech index:6, len 0.72, rtf 0.236,  cost 0.170s,  all cost time 1.626s\n", "100%|██████████| 1/1 [00:01<00:00,  1.63s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["任务完成，生成 7 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["task_id = 0\n", "\n", "tts_text = '收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。'\n", "\n", "chunk_num = 0\n", "audio_data: torch.Tensor = None\n", "async for chunk in cosyvoice.inference_zero_shot_by_spk_id(tts_text, spk_id='xiaohe', stream=True):\n", "    audio_data = torch.concat([audio_data, chunk['tts_speech']], dim=1) if audio_data is not None else chunk['tts_speech']\n", "    chunk_num += 1\n", "torchaudio.save('zero_shot_{}.wav'.format(task_id), audio_data, cosyvoice.sample_rate)\n", "print(f'任务完成，生成 {chunk_num} 个片段')\n"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]2025-03-02 17:30:45,887 INFO synthesis text 收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:30:46,961 INFO llm job done, time cost: 1.072s\n", "2025-03-02 17:30:46,962 DEBUG speech_tokens: len: 274  data: [1490, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 2112, 3672, 6454, 6374, 46, 632, 4852, 300, 4592, 5591, 2432, 6075, 6075, 6085, 5591, 4946, 4844, 5020, 4993, 4591, 5076, 5009, 630, 5644, 2682, 2831, 1805, 1915, 4130, 224, 5189, 4539, 4266, 5724, 5027, 4951, 4473, 3994, 5650, 4407, 809, 1611, 59, 3653, 166, 147, 4769, 5173, 6454, 288, 59, 74, 5851, 6099, 6381, 4528, 2413, 873, 4850, 4939, 5101, 5340, 5070, 6451, 6559, 5830, 716, 596, 514, 1701, 5832, 5835, 5919, 5109, 1788, 2040, 3666, 2921, 5834, 2512, 6507, 2279, 4769, 3481, 3471, 2024, 1376, 1937, 2914, 2621, 5921, 220, 76, 4616, 1050, 5405, 4407, 1295, 1349, 2051, 4076, 4723, 4513, 5489, 4732, 4965, 5019, 2753, 1375, 4129, 5341, 4440, 6455, 2493, 59, 2234, 5206, 6373, 2196, 4513, 3750, 4507, 4569, 1038, 4918, 1170, 2186, 1373, 5831, 6462, 6535, 6546, 6559, 719, 623, 1947, 6075, 3645, 1461, 2031, 6534, 2261, 3890, 2696, 4319, 2879, 4834, 4408, 4834, 4677, 566, 296, 5857, 6261, 5649, 4527, 5081, 2807, 5044, 3912, 4672, 3402, 503, 1427, 2128, 1882, 2546, 5920, 2394, 4573, 4970, 1110, 2490, 4704, 4704, 2769, 5012, 2195, 5111, 912, 4317, 2618, 484, 795, 4616, 1434, 654, 2838, 2686, 5356, 3656, 1460, 1466, 440, 2420, 5908, 6149, 4700, 5023, 599, 1358, 2060, 2113, 4299, 4299, 4299, 4299, 4299, 4299, 2112, 5838, 5241, 4759, 312, 1460, 1217, 2789, 5090, 3474, 5661, 5086, 4991, 4235, 3913, 3963, 3638, 3644, 654, 5754, 3570, 692, 1949, 1703, 3971, 5995, 5268, 4575, 1161, 1920, 4733, 4463, 4567, 6378, 5403, 4445, 278, 737, 737, 2657, 6453, 6540, 4969, 6157, 6077, 3404, 1235, 1993, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 4299]\n", "2025-03-02 17:30:47,151 INFO yield speech len 10.96, rtf 0.11538138789852169\n", "100%|██████████| 1/1 [00:01<00:00,  1.27s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["任务完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["task_id = 0\n", "\n", "tts_text = '收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。'\n", "\n", "chunk_num = 0\n", "audio_data: torch.Tensor = None\n", "async for chunk in cosyvoice.inference_instruct2_by_spk_id(tts_text, '使用四川话说', spk_id='xiaohe', stream=False):\n", "    audio_data = torch.concat([audio_data, chunk['tts_speech']], dim=1) if audio_data is not None else chunk['tts_speech']\n", "    chunk_num += 1\n", "torchaudio.save('instruct2_{}.wav'.format(task_id), audio_data, cosyvoice.sample_rate)\n", "print(f'任务完成，生成 {chunk_num} 个片段')\n"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]2025-03-02 17:30:47,170 INFO synthesis text 收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:30:47,425 INFO yield speech len 0.44, rtf 0.5776085636832498\n", "2025-03-02 17:30:47,425 DEBUG multiples: 1.95, peer_chunk_token_num: 15\n", "2025-03-02 17:30:47,526 INFO yield speech len 0.6, rtf 0.1687467098236084\n", "2025-03-02 17:30:47,527 DEBUG multiples: 5.87, peer_chunk_token_num: 30\n", "2025-03-02 17:30:47,684 INFO yield speech len 1.2, rtf 0.1307866970698039\n", "2025-03-02 17:30:47,684 DEBUG multiples: 12.57, peer_chunk_token_num: 45\n", "2025-03-02 17:30:47,895 INFO yield speech len 1.8, rtf 0.11709372202555338\n", "2025-03-02 17:30:47,908 DEBUG multiples: 20.48, peer_chunk_token_num: 60\n", "2025-03-02 17:30:48,187 INFO yield speech len 2.4, rtf 0.11635651191075644\n", "2025-03-02 17:30:48,200 DEBUG multiples: 28.72, peer_chunk_token_num: 75\n", "2025-03-02 17:30:48,560 INFO yield speech len 3.0, rtf 0.11986398696899414\n", "2025-03-02 17:30:48,570 DEBUG multiples: 36.72, peer_chunk_token_num: 90\n", "2025-03-02 17:30:48,590 INFO llm job done, time cost: 1.418s\n", "2025-03-02 17:30:48,590 DEBUG speech_tokens: len: 268  data: [1598, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 3651, 6211, 6457, 63, 299, 2655, 1269, 4483, 6077, 3412, 6075, 5841, 5591, 4865, 5051, 5342, 5749, 4615, 4808, 2819, 4917, 3348, 2695, 3611, 3752, 4103, 2663, 5188, 4780, 6453, 4977, 4946, 4465, 6015, 1806, 5408, 2499, 1295, 1613, 1775, 5837, 2353, 786, 4535, 2267, 6454, 6453, 47, 56, 3907, 6366, 6381, 4600, 2414, 1530, 2672, 4939, 5092, 4614, 5094, 5829, 6531, 3643, 707, 542, 2000, 1947, 3645, 5835, 5835, 1545, 4299, 1644, 749, 1460, 5429, 6534, 2790, 4406, 2726, 1241, 1268, 1295, 1294, 6073, 5826, 5085, 5918, 220, 876, 2420, 1842, 5408, 376, 620, 1808, 4157, 5533, 5161, 5242, 5245, 4992, 5020, 566, 1285, 2031, 3975, 2112, 3975, 1671, 6316, 4534, 6454, 6458, 2234, 56, 6094, 6366, 6373, 4474, 831, 5128, 4839, 147, 4920, 3294, 2102, 2105, 2186, 5830, 6534, 6534, 6555, 5829, 2897, 626, 2002, 1945, 1701, 3645, 1461, 4218, 6454, 316, 1703, 1234, 2051, 2870, 4601, 4429, 5408, 501, 3212, 5934, 4155, 4680, 4754, 5075, 5773, 6342, 4671, 5592, 611, 2128, 2130, 2390, 5191, 4609, 4421, 4700, 459, 303, 4785, 2445, 4733, 4382, 2389, 4314, 2618, 241, 66, 4607, 2139, 651, 3486, 3169, 1703, 1460, 1466, 737, 3800, 5986, 4456, 5026, 2789, 602, 2086, 1945, 3969, 3648, 5838, 3003, 1788, 5943, 5323, 5489, 321, 734, 1949, 494, 629, 2828, 5418, 5661, 4834, 5081, 3509, 2048, 3939, 6229, 5831, 3643, 5028, 3570, 2906, 2033, 1946, 1712, 3980, 6026, 5292, 1188, 945, 4733, 4462, 4543, 141, 6459, 4425, 2465, 737, 734, 146, 6534, 6534, 5024, 6077, 6077, 6077, 1955, 1954, 4299, 6486, 6486, 6486, 6486, 6486, 4299, 4299]\n", "2025-03-02 17:30:48,763 INFO yield speech len 1.28, rtf 0.15058256685733795\n", "100%|██████████| 1/1 [00:01<00:00,  1.59s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["任务完成，生成 7 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["task_id = 0\n", "\n", "tts_text = '收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。'\n", "\n", "chunk_num = 0\n", "audio_data: torch.Tensor = None\n", "async for chunk in cosyvoice.inference_instruct2_by_spk_id(tts_text, '使用四川话说', spk_id='xiaohe', stream=True):\n", "    audio_data = torch.concat([audio_data, chunk['tts_speech']], dim=1) if audio_data is not None else chunk['tts_speech']\n", "    chunk_num += 1\n", "torchaudio.save('instruct2_{}.wav'.format(task_id), audio_data, cosyvoice.sample_rate)\n", "print(f'任务完成，生成 {chunk_num} 个片段')\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["import time\n", "import asyncio\n", "import torchaudio\n", "from typing import AsyncGenerator\n", "\n", "async def test_concurrent_instruct(num_tasks: int = 3, semaphore_limit: int = 5, stream=False):\n", "    \"\"\"\n", "    异步并发测试函数，用于验证多个推理任务并行执行能力\n", "    \n", "    参数：\n", "    num_tasks: 并发任务数量，默认3个\n", "    \n", "    功能特点：\n", "    1. 动态创建多个异步生成器任务\n", "    2. 使用信号量控制并发度（默认限制5个）\n", "    3. 实时跟踪任务完成进度\n", "    4. 自动处理异常并记录错误\n", "    \"\"\"\n", "    semaphore = asyncio.Semaphore(semaphore_limit)  # 并发控制\n", "    \n", "    async def single_task(task_id: int):\n", "        \"\"\"单个推理任务处理流程\"\"\"\n", "        async with semaphore:\n", "            try:\n", "                # text_gen = (chunk for chunk in [\n", "                #     f'这是任务{task_id}的第一句话，',\n", "                #     f'测试并发处理能力，',\n", "                #     f'当前进度：{task_id}-第三部分'\n", "                # ])\n", "                text_gen = f'''这是任务{task_id}，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。'''\n", "                # 记录保存索引\n", "                save_index = 0\n", "                \n", "                # 流式处理\n", "                audio_data: torch.Tensor = None\n", "                async for chunk in cosyvoice.inference_zero_shot_by_spk_id(\n", "                    text_gen,\n", "                    'xiaohe',\n", "                    # prompt_text,\n", "                    # prompt_speech_16k,\n", "                    stream=stream,\n", "                ):\n", "                    audio_data = torch.concat([audio_data, chunk['tts_speech']], dim=1) if audio_data is not None else chunk['tts_speech']\n", "                    save_index += 1\n", "                    print(f'任务 {task_id} 进度：{save_index}')\n", "                # 保存音频片段\n", "                torchaudio.save('tts_speech_{}.wav'.format(task_id), audio_data, cosyvoice.sample_rate)\n", "                    \n", "                print(f'任务 {task_id} 完成，生成 {save_index} 个片段')\n", "                \n", "            except Exception as e:\n", "                print(f'任务 {task_id} 异常: {str(e)}')\n", "    \n", "    # 创建并发任务\n", "    tasks = [single_task(i) for i in range(num_tasks)]\n", "    \n", "    # 执行并等待\n", "    await asyncio.gather(*tasks)\n"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]2025-03-02 17:31:42,806 INFO synthesis text 这是任务零，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:31:42,825 INFO synthesis text 这是任务一，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\u001b[A2025-03-02 17:31:42,837 INFO synthesis text 这是任务二，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\u001b[A\u001b[A2025-03-02 17:31:42,845 INFO synthesis text 这是任务三，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A2025-03-02 17:31:42,853 INFO synthesis text 这是任务四，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:31:44,300 INFO llm job done, time cost: 1.429s\n", "2025-03-02 17:31:44,301 DEBUG speech_tokens: len: 284  data: [2032, 4131, 3645, 3645, 3645, 3645, 3645, 3645, 3645, 1866, 2031, 4673, 4501, 4447, 4680, 5256, 5904, 5895, 5338, 4807, 2861, 3590, 4346, 1454, 2990, 6373, 4185, 2216, 4376, 4394, 5205, 4413, 4753, 4752, 2657, 5095, 300, 4484, 6401, 3412, 5589, 4144, 1304, 2894, 4615, 4615, 704, 713, 318, 4836, 1062, 1322, 3590, 5530, 6289, 6316, 3158, 89, 767, 2047, 6537, 1440, 5678, 4880, 4474, 1887, 3462, 4675, 4938, 4777, 5986, 38, 1463, 35, 66, 59, 5170, 4833, 4833, 144, 4493, 5203, 5286, 5313, 6070, 3881, 1585, 1587, 52, 1376, 1772, 2012, 3641, 6560, 3644, 2186, 2176, 2028, 3888, 3645, 3645, 3645, 3645, 1701, 4299, 3990, 5122, 5186, 4700, 6534, 6534, 4727, 4412, 2240, 4966, 1259, 5641, 5073, 4992, 5507, 4370, 2914, 5753, 5995, 2276, 801, 143, 2267, 2004, 4676, 2256, 5020, 5533, 5529, 5532, 4434, 4512, 4674, 312, 2023, 1376, 1862, 1861, 1942, 3724, 4590, 4996, 2484, 2225, 4501, 4476, 4672, 4428, 4519, 2934, 2234, 56, 1047, 5730, 6294, 5337, 5825, 4373, 4291, 6507, 6537, 2185, 2102, 1370, 3884, 6062, 5270, 5672, 2861, 728, 704, 2258, 4510, 4678, 294, 5074, 6262, 1725, 4755, 3395, 5102, 5776, 6501, 2733, 5562, 3321, 5038, 5074, 5776, 6501, 2854, 3890, 734, 1532, 5, 2246, 1932, 786, 4704, 2760, 566, 35, 910, 6501, 3832, 5100, 795, 59, 1125, 654, 651, 4947, 5761, 5994, 5921, 359, 149, 5987, 3718, 2336, 2585, 380, 1757, 2059, 3648, 5835, 5838, 5838, 1545, 6081, 4593, 4756, 2337, 4543, 6400, 2867, 4124, 3881, 1199, 728, 5045, 5960, 3799, 2102, 6560, 5830, 4947, 5028, 2841, 5057, 5755, 6481, 6238, 5995, 3836, 1679, 3143, 2175, 2169, 2999, 2921, 4406, 3669, 6262, 2148, 6380, 2260, 5188, 5, 1586, 2047, 6537, 4320, 3485, 1217, 1226, 1229, 2113, 4299]\n", "2025-03-02 17:31:44,323 INFO llm job done, time cost: 1.455s\n", "2025-03-02 17:31:44,324 DEBUG speech_tokens: len: 289  data: [2031, 5832, 5832, 5835, 5835, 3729, 2031, 5402, 4501, 4447, 5643, 5176, 5176, 5905, 4528, 4564, 5774, 6506, 4346, 4373, 1127, 3722, 3719, 1531, 467, 2807, 3266, 6181, 5532, 4227, 6486, 4218, 5832, 5832, 5832, 3645, 1782, 5376, 5725, 2250, 2903, 5086, 306, 4511, 5023, 4943, 5589, 6318, 1946, 3491, 2897, 5582, 5587, 4853, 725, 1373, 1275, 4833, 3483, 674, 4238, 6260, 6208, 6313, 2411, 5, 767, 1804, 6534, 2765, 4943, 4880, 6015, 4191, 5405, 4533, 4531, 2990, 2, 3650, 2303, 66, 56, 65, 5726, 6372, 4439, 4396, 5125, 5125, 5152, 4448, 2990, 3799, 805, 566, 1862, 2180, 2186, 5831, 5831, 1457, 1448, 2112, 4131, 5832, 5832, 3645, 1869, 1725, 5877, 4490, 2420, 6537, 4356, 2621, 4490, 4885, 4966, 4911, 5344, 3640, 3642, 5026, 5185, 2276, 159, 74, 77, 2094, 5405, 2265, 4778, 5695, 5047, 4440, 4673, 4672, 2496, 5912, 3239, 2024, 1943, 4130, 3806, 4509, 5644, 4421, 4396, 5205, 5410, 5418, 4518, 5205, 2934, 2243, 1531, 2013, 5565, 4365, 5827, 2105, 4279, 6534, 6537, 2182, 1454, 2912, 5099, 4299, 4215, 5832, 5832, 5835, 1545, 1581, 3883, 5270, 4952, 3590, 728, 137, 4432, 5483, 2229, 4994, 5209, 6261, 3237, 5490, 3638, 2879, 5692, 5529, 1257, 4836, 3564, 4957, 5064, 6501, 6501, 5032, 5834, 20, 3206, 245, 56, 1842, 2220, 2679, 260, 818, 1881, 6502, 6020, 5101, 78, 2263, 56, 1453, 1383, 651, 4947, 6328, 5994, 5192, 71, 5906, 6148, 2243, 461, 596, 1972, 3645, 5916, 5916, 5943, 3975, 2112, 4407, 4593, 4512, 4603, 4780, 2900, 4124, 1937, 1451, 692, 5693, 4016, 4043, 1454, 3644, 5028, 4947, 2850, 5056, 6404, 6481, 5995, 1652, 1685, 2178, 2163, 812, 5108, 5124, 3751, 5647, 4439, 5104, 11, 1558, 1806, 6537, 4356, 5024, 6320, 1226, 1958, 4215, 4299, 4299, 3891, 3972, 3975, 5916, 3975]\n", "2025-03-02 17:31:44,587 INFO llm job done, time cost: 1.716s\n", "2025-03-02 17:31:44,587 DEBUG speech_tokens: len: 304  data: [1948, 3402, 6075, 5859, 3888, 6075, 3645, 3888, 3645, 1542, 2085, 5648, 4447, 4762, 5419, 4672, 5175, 5905, 6390, 4447, 4564, 5044, 5777, 6533, 6559, 5090, 1976, 6157, 5508, 6157, 1784, 1541, 1652, 1679, 5133, 4996, 135, 5090, 2655, 1272, 4727, 5675, 2677, 6318, 4144, 1223, 2894, 4853, 5587, 5345, 470, 719, 2093, 1032, 5562, 2757, 1403, 4313, 6316, 2672, 737, 1490, 2071, 6537, 1387, 4136, 4880, 6018, 4191, 5405, 4749, 4534, 3800, 3653, 3653, 32, 2013, 74, 2251, 6212, 5645, 2261, 4421, 5123, 5149, 4447, 4451, 1617, 798, 1295, 1862, 2180, 2185, 6560, 5827, 5831, 2183, 2092, 4299, 4299, 4218, 4299, 4299, 2058, 60, 3003, 3645, 3645, 3645, 1461, 1950, 3750, 3663, 5266, 6238, 3323, 6537, 6507, 4601, 4517, 4912, 4912, 5640, 4777, 6553, 5101, 5510, 5192, 59, 315, 2246, 1851, 5651, 2256, 5669, 5776, 4803, 4432, 5405, 4679, 4678, 2499, 1781, 1295, 1052, 2015, 2104, 4129, 1943, 2983, 4833, 4671, 74, 4394, 4419, 4753, 2260, 4420, 6099, 4421, 2342, 1776, 5727, 3384, 5581, 5828, 4288, 6534, 6534, 4347, 2186, 2105, 1454, 3641, 4370, 2009, 1762, 1525, 5989, 5999, 5671, 674, 1403, 707, 2987, 5483, 2247, 2834, 5696, 6258, 3993, 4755, 2666, 5081, 5048, 5774, 6258, 3471, 5562, 3483, 5066, 5047, 6501, 4311, 5914, 3650, 803, 3719, 2, 67, 1275, 2193, 501, 278, 737, 1881, 6501, 3347, 4993, 858, 63, 461, 1446, 681, 2841, 5598, 5995, 5995, 5840, 8, 3800, 3719, 2297, 641, 1352, 1975, 1702, 3888, 3645, 5832, 5832, 5832, 5916, 5919, 1140, 2949, 4512, 4759, 2337, 4564, 5674, 5081, 3395, 3883, 1928, 707, 674, 5777, 5773, 2002, 1750, 3717, 1612, 2183, 5830, 2760, 5025, 2841, 5093, 5755, 6409, 6238, 6239, 3812, 1679, 1676, 2166, 469, 3650, 4382, 5856, 1806, 6377, 2260, 5192, 2921, 1801, 2049, 6537, 3627, 5675, 3413, 1235, 1951, 2031, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 4218]\n", "2025-03-02 17:31:44,723 INFO yield speech index:0, len 11.36, rtf 0.165,  cost 1.878s,  all cost time 1.878s\n", "\n", "\n", "100%|██████████| 1/1 [00:01<00:00,  1.88s/it]\n", "2025-03-02 17:31:44,780 INFO llm job done, time cost: 1.908s\n", "2025-03-02 17:31:44,781 DEBUG speech_tokens: len: 316  data: [2112, 4218, 3888, 5832, 5835, 5835, 5835, 3651, 4299, 3462, 5401, 4447, 5167, 5644, 5905, 3963, 6390, 4520, 4817, 5777, 5804, 6560, 5744, 4752, 4672, 5401, 5409, 6147, 5177, 1532, 1517, 1759, 3681, 4833, 4996, 2565, 2909, 5094, 1842, 2332, 5509, 3485, 3402, 6318, 4145, 1301, 2897, 5096, 5587, 2429, 461, 710, 1364, 1032, 4833, 1305, 1430, 5777, 5449, 6316, 3155, 113, 41, 830, 4353, 1440, 5675, 4879, 5203, 4074, 5650, 4516, 4695, 4777, 3718, 38, 3650, 2195, 147, 2251, 2243, 4429, 5483, 5401, 2261, 4493, 5287, 6043, 6043, 6071, 3773, 1614, 80, 3481, 1862, 2180, 6560, 6556, 4370, 1454, 2093, 2112, 1944, 3888, 5913, 5943, 5835, 5835, 5835, 5835, 2112, 1806, 5853, 5105, 6157, 4023, 6537, 2638, 4511, 4412, 2207, 1478, 1498, 2112, 1951, 1988, 4912, 5019, 5587, 6554, 5099, 5510, 5918, 2330, 315, 2252, 807, 5650, 4434, 3563, 5776, 5046, 4749, 4429, 4675, 4435, 4677, 1781, 3482, 2753, 1295, 2087, 4299, 1987, 1862, 3806, 3887, 4130, 4049, 1862, 1763, 1762, 5133, 5644, 5644, 4448, 4438, 5124, 5653, 5401, 4448, 4473, 5853, 4412, 47, 1749, 5730, 6291, 6310, 5097, 3644, 4288, 6535, 6538, 6540, 4368, 4370, 1457, 725, 2912, 2093, 2113, 1947, 6075, 5832, 5832, 5859, 5832, 3645, 4299, 6486, 6486, 6486, 6486, 6486, 1797, 3720, 4568, 5428, 5048, 5777, 719, 389, 4431, 4594, 2220, 5102, 5938, 6181, 5247, 3395, 5063, 5777, 5776, 6261, 2490, 2403, 3414, 5047, 5773, 6498, 5033, 6158, 2918, 3908, 263, 29, 1689, 789, 2274, 512, 278, 121, 4071, 4314, 5048, 4857, 72, 2219, 1443, 651, 2838, 5680, 5510, 3005, 143, 3719, 5986, 2243, 380, 2000, 2000, 6486, 6486, 4299, 5136, 4590, 4512, 2311, 4780, 5078, 3800, 3881, 1415, 2861, 5777, 5447, 1507, 1612, 2180, 5831, 4947, 5028, 2879, 6481, 6237, 5996, 3839, 1652, 1198, 2163, 650, 5348, 4385, 2206, 5937, 2013, 6374, 4442, 4379, 20, 1805, 6456, 6534, 713, 5753, 3413, 1226, 1952, 6486, 6486, 6486, 6486, 6486, 6486, 4299]\n", "2025-03-02 17:31:44,836 INFO llm job done, time cost: 1.966s\n", "2025-03-02 17:31:44,837 DEBUG speech_tokens: len: 319  data: [1947, 3645, 5835, 5832, 5835, 5835, 3651, 5835, 5838, 3651, 1869, 4299, 2112, 2733, 4672, 4447, 5177, 4671, 5176, 5177, 2991, 5178, 4564, 4988, 3590, 5777, 5803, 5828, 3158, 5668, 5641, 4912, 4777, 4992, 4830, 2112, 3648, 5835, 3648, 5916, 5835, 3645, 2112, 3648, 5454, 4752, 2322, 4853, 5095, 1272, 4484, 4943, 2686, 6327, 1955, 1304, 680, 5339, 6559, 5345, 704, 725, 1050, 4833, 1224, 674, 5690, 6316, 6315, 3155, 110, 767, 1882, 6453, 4356, 1301, 4880, 5205, 6261, 6379, 5405, 4911, 4525, 2989, 731, 1460, 737, 71, 1860, 65, 110, 389, 389, 2993, 4590, 4833, 2314, 4520, 5126, 5206, 5205, 5152, 3718, 3799, 802, 485, 1133, 2093, 2186, 5804, 5831, 2186, 2186, 2140, 2025, 3888, 3645, 3648, 5835, 5835, 5835, 5835, 5838, 1545, 4299, 2121, 5853, 4456, 4709, 4275, 6507, 4574, 4669, 4885, 4884, 4992, 5344, 5830, 5102, 5267, 4544, 76, 73, 152, 4191, 4686, 2915, 6262, 6262, 5529, 4443, 5483, 5405, 4676, 2508, 1295, 1295, 1295, 2015, 2096, 1942, 1943, 4130, 1862, 3725, 4509, 4915, 46, 4439, 5125, 5397, 4680, 4447, 5121, 6096, 4430, 883, 3471, 6051, 6310, 6556, 4373, 6465, 6535, 6534, 2184, 2186, 1457, 2186, 2099, 3883, 6026, 6239, 2861, 3590, 719, 149, 4509, 4515, 375, 6263, 6502, 6261, 5415, 3151, 5084, 5048, 5772, 1833, 6051, 1134, 5047, 5775, 6501, 3583, 5429, 974, 776, 1019, 1046, 380, 2342, 1923, 2220, 4947, 538, 35, 1881, 2128, 4068, 5048, 4830, 1779, 59, 1441, 654, 2838, 4869, 5429, 737, 791, 5906, 3961, 2495, 299, 1352, 1944, 4299, 3648, 5835, 5832, 5832, 3645, 3645, 5835, 3648, 5835, 3729, 4299, 4299, 4299, 4299, 4299, 4299, 5109, 4485, 4918, 2319, 5265, 5026, 2903, 3881, 3881, 1199, 626, 2888, 3590, 6506, 6179, 6178, 5935, 5935, 6259, 6181, 4021, 1616, 4370, 2877, 5028, 5028, 2887, 4946, 5428, 5428, 4943, 677, 1163, 3134, 1926, 2160, 434, 5105, 4414, 5856, 1887, 6380, 4450, 5188, 2, 1802, 3992, 6453, 6534, 721, 3404, 506, 1229, 1951, 4299]\n", "2025-03-02 17:31:44,862 INFO yield speech index:0, len 11.56, rtf 0.178,  cost 2.056s,  all cost time 2.056s\n", "100%|██████████| 1/1 [00:02<00:00,  2.06s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 3 进度：1\n", "任务 3 完成，生成 1 个片段\n", "任务 0 进度：1\n", "任务 0 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:31:45,135 INFO yield speech index:0, len 12.16, rtf 0.189,  cost 2.298s,  all cost time 2.298s\n", "\n", "100%|██████████| 1/1 [00:02<00:00,  2.31s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 2 进度：1\n", "任务 2 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:31:45,457 INFO yield speech index:0, len 12.76, rtf 0.206,  cost 2.632s,  all cost time 2.632s\n", "100%|██████████| 1/1 [00:02<00:00,  2.63s/it]\n", "2025-03-02 17:31:45,462 INFO yield speech index:0, len 12.64, rtf 0.206,  cost 2.609s,  all cost time 2.609s\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:02<00:00,  2.61s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 1 进度：1\n", "任务 1 完成，生成 1 个片段\n", "任务 4 进度：1\n", "任务 4 完成，生成 1 个片段\n", "--- 2.6984400749206543 seconds ---\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["start_time = time.time()\n", "await test_concurrent_instruct(5, semaphore_limit=10)\n", "print(\"--- %s seconds ---\" % (time.time() - start_time))"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]2025-03-02 17:31:46,799 INFO synthesis text 这是任务零，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:31:46,837 INFO synthesis text 这是任务一，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\u001b[A2025-03-02 17:31:46,871 INFO synthesis text 这是任务二，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\u001b[A\u001b[A2025-03-02 17:31:46,887 INFO synthesis text 这是任务三，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A2025-03-02 17:31:46,901 INFO synthesis text 这是任务四，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:46,912 INFO synthesis text 这是任务五，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:46,920 INFO synthesis text 这是任务六，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:46,930 INFO synthesis text 这是任务七，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:46,937 INFO synthesis text 这是任务八，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:46,945 INFO synthesis text 这是任务九，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:31:48,000 INFO llm job done, time cost: 1.051s\n", "2025-03-02 17:31:48,001 DEBUG speech_tokens: len: 269  data: [3645, 5832, 5835, 3648, 2112, 5648, 2466, 4528, 5418, 5652, 6148, 5419, 6147, 4690, 4565, 5069, 5777, 6532, 6559, 5828, 966, 3462, 4676, 4408, 2517, 5750, 4777, 5401, 4915, 2828, 5089, 387, 4439, 4537, 3161, 6318, 6321, 1223, 1385, 2900, 4858, 4613, 644, 1368, 4918, 2835, 593, 2129, 6290, 6315, 5345, 113, 767, 2070, 6456, 2675, 2768, 4879, 3831, 6379, 4679, 4749, 4615, 2989, 1466, 5, 66, 2252, 2255, 6211, 6131, 2466, 4421, 4421, 5149, 5230, 5176, 2264, 1536, 79, 647, 1862, 2093, 2186, 5828, 6560, 4373, 2183, 2055, 3645, 5832, 5859, 5859, 5859, 3645, 1788, 6486, 3990, 5851, 5186, 4780, 6456, 3546, 4484, 4526, 4966, 4885, 4912, 5587, 6553, 5078, 5270, 4382, 801, 2216, 76, 4191, 5651, 3237, 5048, 6261, 4803, 4435, 5405, 4679, 312, 1295, 1295, 2105, 1943, 6317, 4434, 6212, 6374, 2233, 4424, 4477, 5401, 5643, 4439, 4473, 5937, 4438, 2252, 2013, 4998, 3294, 6554, 5830, 3644, 4288, 6535, 6537, 2185, 1370, 2993, 3231, 4406, 5509, 5039, 701, 632, 2258, 4510, 4435, 2454, 5696, 6261, 3264, 4512, 5096, 5047, 5773, 3471, 4753, 2511, 4956, 5047, 5772, 4312, 3890, 11, 3962, 5, 147, 2004, 2193, 4866, 539, 35, 1641, 1884, 4967, 5100, 801, 56, 1368, 654, 2838, 2757, 5438, 5270, 2195, 3719, 6230, 2261, 2243, 56, 1732, 5832, 5940, 5940, 5943, 2922, 2031, 4432, 4435, 2301, 4540, 5026, 2870, 317, 3477, 551, 674, 5774, 6122, 5412, 1855, 2186, 5831, 2905, 5028, 5028, 2770, 5753, 6481, 6023, 3839, 950, 2160, 1434, 434, 5111, 4388, 5850, 5935, 1806, 6380, 5644, 4406, 5185, 92, 1829, 4248, 6534, 3591, 6401, 5591, 2693, 1967, 2112, 4299, 6486, 4218, 6486, 6486, 6486, 6081]\n", "2025-03-02 17:31:48,087 INFO llm job done, time cost: 1.138s\n", "2025-03-02 17:31:48,087 DEBUG speech_tokens: len: 276  data: [2032, 4131, 5835, 5835, 5916, 5835, 5916, 5919, 3732, 2112, 5648, 2259, 4528, 4437, 5644, 5175, 5904, 5907, 5176, 4600, 4735, 5777, 6505, 5803, 5830, 4346, 2156, 2132, 2159, 2159, 2177, 2087, 1765, 5319, 4837, 144, 5090, 5013, 303, 4511, 6481, 3404, 5346, 6327, 1949, 2897, 5096, 5587, 2429, 713, 728, 1119, 4755, 1305, 1403, 6419, 6316, 5588, 113, 1100, 6456, 3600, 5678, 4963, 6015, 4200, 4676, 4686, 5100, 2347, 803, 1460, 737, 148, 74, 2990, 4671, 4753, 4671, 4448, 4394, 5126, 5152, 5175, 5259, 2341, 3718, 1534, 1052, 2024, 1859, 4373, 6532, 6533, 4373, 2096, 2032, 3888, 5862, 5946, 3759, 1458, 2031, 6018, 4384, 6158, 4185, 6435, 2576, 4751, 5694, 4911, 5667, 4992, 4858, 6554, 5830, 5078, 4460, 67, 803, 805, 2067, 4921, 4434, 5021, 5777, 6178, 4800, 4416, 4513, 4435, 4650, 1295, 2024, 1943, 2186, 4130, 3724, 4752, 4673, 4437, 4442, 5122, 4422, 4429, 4501, 4419, 6096, 317, 1531, 5730, 6294, 6552, 5831, 6465, 6535, 6537, 2104, 2105, 4373, 2186, 2059, 6156, 3648, 5838, 3651, 1869, 3883, 6026, 5026, 5777, 3617, 1442, 5164, 4590, 4569, 5075, 5452, 3994, 5412, 4772, 5066, 5776, 5773, 4917, 3321, 2860, 5074, 5046, 6501, 5024, 5834, 2, 3800, 281, 2243, 2085, 2217, 2517, 592, 116, 1881, 4314, 5290, 5100, 804, 65, 1453, 654, 648, 5598, 5996, 3653, 35, 5987, 3962, 4520, 389, 623, 2083, 3645, 5835, 3651, 2922, 2031, 5217, 4756, 4756, 4515, 4567, 5752, 5054, 2912, 4124, 3881, 1190, 692, 2861, 5776, 6424, 2006, 2005, 2077, 4235, 4235, 4346, 6559, 5028, 5028, 5028, 2878, 6485, 6481, 6238, 5996, 1649, 3863, 2172, 1440, 5915, 5108, 2937, 3750, 6379, 4680, 4541, 5834, 38, 1804, 6456, 6534, 2873, 6323, 1235, 1963]\n", "2025-03-02 17:31:48,223 INFO llm job done, time cost: 1.275s\n", "2025-03-02 17:31:48,223 DEBUG speech_tokens: len: 284  data: [5346, 5103, 5859, 5859, 5862, 1542, 4218, 5401, 2205, 5175, 5644, 4447, 5905, 6390, 4451, 4564, 5777, 4319, 3644, 3557, 5157, 6455, 5402, 5645, 5175, 2990, 803, 1516, 1701, 5832, 5862, 1461, 4299, 5133, 5725, 4752, 4772, 387, 306, 4780, 4943, 5589, 5589, 3415, 575, 683, 5825, 5830, 5344, 5078, 635, 882, 5001, 891, 593, 3590, 6178, 5587, 224, 788, 1909, 6540, 4347, 4133, 5609, 4717, 4071, 5649, 4431, 4525, 3718, 56, 1463, 2246, 1776, 2990, 5158, 5483, 2241, 4433, 5153, 5234, 6070, 6062, 884, 1506, 79, 566, 1295, 1849, 2096, 5830, 6559, 2186, 2084, 3159, 5832, 5862, 5862, 5838, 5835, 1461, 4299, 6486, 3909, 4411, 4699, 6534, 4329, 4484, 4427, 4886, 5641, 5020, 5341, 2887, 5054, 5266, 4382, 876, 77, 879, 3462, 2256, 5021, 6262, 5532, 4416, 5323, 5486, 2508, 566, 566, 2024, 2015, 4129, 4373, 2996, 4672, 5400, 4415, 5125, 6393, 5409, 4447, 5853, 3909, 4448, 804, 1248, 5562, 5581, 5831, 2186, 6534, 6534, 6507, 2185, 1457, 725, 1757, 1759, 5904, 4460, 6158, 2852, 701, 470, 2257, 4753, 4675, 4697, 3751, 6262, 2490, 3152, 5066, 5045, 3831, 5244, 1647, 2685, 5039, 6501, 6498, 2846, 5834, 767, 1504, 1775, 758, 793, 2166, 4435, 4380, 2445, 314, 35, 1666, 2130, 4315, 5533, 5073, 861, 66, 140, 1443, 654, 3486, 6318, 5266, 4543, 35, 1532, 5986, 74, 2324, 551, 1271, 1948, 5832, 5832, 5859, 5862, 5862, 3648, 2112, 5136, 5482, 4675, 4507, 5509, 5027, 716, 1937, 3880, 1937, 626, 2888, 6506, 5774, 4015, 1856, 2099, 3641, 2915, 5028, 5028, 2770, 6485, 6401, 5752, 5510, 1622, 1652, 1676, 2169, 2160, 1136, 5108, 2236, 5857, 1905, 5648, 4522, 5188, 812, 1802, 1804, 6432, 6537, 711, 5752, 5591, 506, 1964, 2031, 2112, 1704, 5832, 5832, 5832, 3645, 3645]\n", "2025-03-02 17:31:48,312 INFO yield speech index:0, len 10.76, rtf 0.128,  cost 1.382s,  all cost time 1.382s\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:01<00:00,  1.38s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：1\n", "任务 7 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:31:48,522 INFO llm job done, time cost: 1.572s\n", "2025-03-02 17:31:48,523 DEBUG speech_tokens: len: 302  data: [4299, 4299, 6486, 6486, 6486, 6486, 4299, 1734, 4672, 2250, 5177, 4437, 4446, 5176, 3721, 5178, 4568, 5050, 3590, 4319, 6560, 3394, 1275, 5325, 1699, 704, 629, 2168, 4299, 4299, 4299, 4299, 5136, 4833, 2646, 713, 2584, 2493, 5509, 6158, 3403, 6318, 3415, 575, 602, 5096, 4616, 2426, 716, 1373, 1923, 4512, 3321, 593, 1403, 3506, 6179, 6181, 6316, 224, 764, 1496, 1800, 6537, 6534, 2837, 4871, 4880, 5692, 5532, 2112, 546, 5404, 4695, 4454, 2990, 38, 1463, 1466, 148, 1272, 2333, 137, 623, 1757, 1756, 2112, 3891, 4752, 4996, 4915, 2234, 4421, 5122, 5203, 5178, 4448, 883, 1530, 1133, 2105, 1843, 2074, 6529, 6559, 1454, 2087, 4299, 4299, 6486, 4299, 4218, 2040, 5931, 4403, 4456, 4284, 6543, 4844, 4430, 4421, 4399, 1979, 1951, 4992, 4965, 4993, 3158, 4369, 5090, 5267, 5111, 49, 2013, 65, 137, 56, 1534, 2139, 2490, 4675, 2590, 5777, 6425, 4803, 4431, 4513, 4675, 4677, 3967, 1781, 2015, 2059, 1943, 3887, 1700, 2996, 4590, 4671, 2261, 4474, 4419, 4680, 4519, 3666, 4663, 2486, 1125, 5730, 3393, 5095, 2915, 4278, 6535, 6546, 1457, 725, 1361, 1759, 1615, 5306, 5752, 2861, 701, 686, 5146, 4594, 2310, 4993, 6261, 4686, 3150, 2900, 5776, 5529, 1905, 5322, 3348, 2685, 5056, 5043, 4311, 5429, 3650, 731, 1532, 3719, 3961, 245, 623, 1757, 1515, 2139, 2247, 4704, 4704, 582, 647, 251, 8, 1557, 4072, 4314, 5317, 5073, 4940, 1941, 1026, 2246, 299, 1361, 2086, 2112, 651, 5025, 6166, 5998, 2519, 62, 3718, 3799, 74, 785, 1756, 4299, 6486, 6486, 6486, 4218, 5835, 4509, 4675, 4534, 4537, 5753, 2870, 4367, 3880, 1919, 674, 6506, 5774, 3989, 1747, 1856, 5828, 2886, 5028, 5028, 2841, 5057, 6481, 6400, 6481, 5995, 6026, 1676, 2178, 2166, 712, 3647, 4379, 5853, 3832, 4191, 5418, 4487, 2921, 38, 1829, 6456, 6534, 686, 5591, 1226, 1229, 1948, 4215, 4299, 4299, 4299, 1707]\n", "2025-03-02 17:31:48,570 INFO yield speech index:0, len 11.04, rtf 0.150,  cost 1.659s,  all cost time 1.659s\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:01<00:00,  1.66s/it]\n", "2025-03-02 17:31:48,576 INFO llm job done, time cost: 1.629s\n", "2025-03-02 17:31:48,576 DEBUG speech_tokens: len: 305  data: [4299, 6486, 2031, 4672, 2233, 4771, 5409, 5176, 5176, 5409, 5175, 4564, 4988, 3506, 3617, 5831, 5102, 3395, 4049, 5750, 4912, 4994, 4913, 5642, 5073, 4299, 5352, 5835, 5835, 5835, 5832, 3645, 1785, 5130, 5482, 4752, 2585, 5005, 306, 4673, 5509, 6401, 3485, 5589, 6318, 1955, 494, 5084, 5581, 5586, 4612, 626, 716, 1125, 4755, 3321, 593, 2888, 5773, 6205, 4020, 6313, 2408, 116, 878, 1828, 4251, 6537, 1441, 6404, 4961, 5692, 6177, 2139, 4921, 4407, 5019, 5019, 2267, 2989, 731, 1460, 2222, 1047, 4448, 4438, 5725, 4672, 4448, 4393, 5205, 5418, 5418, 4527, 2261, 3799, 1531, 404, 1294, 1286, 1843, 2087, 2177, 5804, 6532, 4346, 2186, 2186, 2112, 3888, 5916, 5835, 3648, 3648, 1545, 2121, 3744, 5834, 5428, 6462, 6516, 2414, 4751, 5047, 5073, 5100, 5344, 5830, 2864, 4460, 801, 2255, 160, 1275, 4432, 2509, 4994, 5777, 5533, 4444, 5483, 4433, 4678, 2509, 3482, 566, 1295, 1286, 1853, 1859, 4045, 3887, 1619, 5157, 5402, 72, 4420, 5124, 5409, 4501, 4500, 5148, 2243, 802, 2058, 5811, 6309, 5094, 5099, 2102, 6453, 6507, 6507, 4359, 2185, 1457, 725, 2912, 3644, 2087, 2112, 1945, 5832, 5832, 5862, 3648, 1542, 3651, 5238, 2252, 6157, 5044, 6425, 4319, 683, 137, 4428, 4676, 4650, 2834, 5777, 6263, 3480, 3060, 2657, 5048, 5773, 6258, 2742, 4833, 4860, 5038, 5776, 5775, 6504, 5032, 6077, 1460, 749, 3719, 2, 147, 2004, 4461, 2517, 341, 35, 1639, 4315, 4068, 4858, 4992, 2591, 1767, 2246, 641, 1410, 654, 2757, 6075, 5509, 5266, 4463, 59, 5906, 6229, 2503, 4511, 2504, 1271, 1757, 2031, 5832, 5835, 5838, 3732, 2031, 5136, 4510, 4432, 4506, 4780, 5027, 2903, 3881, 1694, 1199, 701, 2861, 5774, 6175, 6147, 4043, 5828, 5101, 4947, 5028, 2779, 5053, 6481, 6481, 6238, 3809, 1625, 1189, 2166, 461, 3650, 2227, 5856, 1887, 5646, 4442, 5188, 2192, 1910, 1802, 6537, 4347, 3566, 6077, 506, 1238, 2040, 4299]\n", "2025-03-02 17:31:48,593 INFO llm job done, time cost: 1.643s\n", "2025-03-02 17:31:48,594 DEBUG speech_tokens: len: 306  data: [2031, 5832, 5835, 5835, 5835, 3648, 2112, 5650, 2223, 4528, 5643, 5409, 4528, 5905, 6390, 5179, 4537, 4964, 4238, 2159, 2186, 2184, 4353, 1440, 4214, 5834, 3647, 6167, 3169, 3891, 5832, 5835, 3651, 1869, 5130, 4996, 4995, 2909, 5092, 225, 2251, 5995, 3404, 3402, 6318, 6318, 1949, 1220, 575, 2897, 4609, 5829, 5586, 4610, 707, 719, 2167, 1299, 6399, 3969, 1785, 2112, 2490, 2646, 498, 1403, 4316, 6178, 6234, 5345, 221, 788, 1747, 6453, 1414, 2678, 5609, 5286, 2076, 4676, 4452, 4525, 2990, 2, 2921, 75, 2225, 2251, 5645, 5402, 45, 4439, 5125, 5125, 4500, 5175, 5906, 1530, 323, 647, 1772, 2003, 2096, 6559, 5802, 4346, 2177, 2112, 4299, 4299, 4299, 6486, 6486, 6486, 4299, 2031, 3663, 5934, 4387, 4733, 2348, 6534, 4320, 4511, 4415, 4886, 5695, 5694, 4965, 5020, 6311, 3644, 2864, 5192, 76, 775, 157, 2004, 4513, 2508, 4993, 5776, 5529, 5529, 4404, 4593, 4515, 2463, 2023, 1376, 2096, 2059, 1852, 3886, 3886, 1781, 4428, 6373, 5643, 4448, 4396, 5209, 6384, 6374, 4420, 4500, 5853, 2252, 74, 1932, 4758, 5562, 5823, 6559, 2186, 4291, 6507, 6510, 2158, 2186, 1454, 1766, 1759, 1776, 4447, 5995, 2861, 4319, 1421, 623, 4444, 4510, 4461, 2780, 6262, 6262, 4683, 3071, 5054, 5044, 3993, 4998, 2646, 2676, 5075, 6501, 6501, 2855, 5915, 11, 1505, 3962, 272, 55, 2166, 786, 2517, 501, 368, 35, 1641, 4315, 3833, 5100, 132, 46, 218, 2166, 681, 2841, 4872, 5509, 6002, 8, 3719, 6229, 4682, 308, 2003, 1944, 6156, 5835, 5835, 5862, 5946, 5835, 5838, 3003, 2112, 5136, 5482, 4432, 2337, 4543, 6401, 710, 1451, 3800, 4124, 713, 608, 2780, 6503, 5689, 3934, 4041, 1937, 2183, 1457, 3644, 5101, 2760, 5028, 5028, 5039, 6481, 6157, 5996, 3839, 1649, 1440, 2139, 712, 3002, 4406, 5856, 1644, 5649, 4672, 4540, 2918, 1801, 3992, 6456, 1440, 5752, 5591, 1238, 1948, 1704, 1785, 4299, 4299, 4299, 4299, 2112]\n", "2025-03-02 17:31:48,612 INFO llm job done, time cost: 1.666s\n", "2025-03-02 17:31:48,612 DEBUG speech_tokens: len: 308  data: [4299, 4299, 4299, 6486, 6486, 6486, 6486, 6486, 6486, 4299, 4191, 5643, 4448, 5419, 6374, 5419, 4448, 3718, 6147, 4522, 4591, 3590, 4346, 3641, 3881, 801, 395, 566, 1241, 3995, 6180, 4299, 6486, 4299, 6486, 6405, 4299, 6486, 6486, 4218, 5838, 4752, 2565, 5095, 2655, 297, 4780, 4942, 3169, 5589, 3412, 575, 2909, 4616, 4601, 2894, 722, 1032, 5562, 586, 674, 5774, 6289, 5586, 4595, 113, 2126, 2157, 1440, 4214, 4871, 5283, 1887, 5650, 4676, 4776, 2428, 2261, 5840, 2249, 801, 74, 5166, 5726, 6374, 2233, 4448, 4421, 5125, 5148, 4501, 4529, 3800, 3880, 80, 1376, 1778, 2068, 4264, 6532, 6530, 4346, 4346, 2041, 4299, 6486, 6486, 6486, 6486, 6486, 6486, 6405, 6405, 6405, 6486, 6405, 4218, 4218, 3747, 4394, 5185, 2169, 6537, 2657, 4568, 2456, 5614, 4993, 4777, 3640, 5090, 5995, 4544, 75, 2246, 59, 2004, 5402, 2238, 5804, 5532, 4506, 4429, 4513, 4407, 5668, 3482, 566, 2105, 1943, 1943, 1862, 4512, 4753, 4671, 4439, 4393, 5151, 4753, 4437, 4438, 5934, 4393, 2216, 1125, 3543, 6294, 6472, 5831, 4292, 6537, 6457, 4275, 2182, 2185, 2186, 4373, 6559, 4308, 4218, 4299, 4218, 4218, 4218, 4218, 4218, 1587, 4451, 5266, 5036, 1403, 707, 887, 4675, 4513, 2482, 5777, 6505, 2499, 3072, 5078, 5777, 5773, 1884, 4920, 5319, 2772, 5061, 6504, 4314, 6158, 1463, 1613, 29, 2243, 1923, 30, 4704, 269, 89, 1884, 4318, 3832, 5100, 861, 68, 137, 2139, 651, 2850, 5680, 5998, 2276, 77, 5909, 3718, 2246, 155, 1757, 2059, 4299, 4299, 6486, 6405, 4299, 4218, 6405, 4299, 6486, 6486, 6486, 6486, 6486, 6405, 4218, 4218, 5136, 4513, 4756, 2346, 5265, 4945, 3638, 3884, 947, 2879, 5777, 5933, 3772, 2099, 4373, 5830, 5025, 5028, 5038, 5755, 5752, 5752, 6239, 1649, 1685, 2166, 703, 5915, 4379, 3669, 3751, 2013, 5644, 4568, 5108, 830, 2045, 2139, 4347, 2837, 6400, 2684, 1235, 1966, 4218, 4299, 4299, 4299, 6486, 6405, 6405, 4218]\n", "2025-03-02 17:31:48,676 INFO llm job done, time cost: 1.729s\n", "2025-03-02 17:31:48,677 DEBUG speech_tokens: len: 311  data: [4299, 4299, 1032, 4671, 4528, 5409, 6455, 4689, 2990, 5907, 5175, 4510, 4736, 4319, 4318, 6532, 5092, 2006, 1951, 6320, 5751, 5672, 1379, 1622, 1649, 2112, 1701, 5913, 3729, 5832, 5916, 3648, 1869, 5133, 5481, 5482, 4753, 2260, 5081, 5097, 1191, 297, 4537, 5428, 1301, 3403, 5589, 6318, 4132, 1223, 2906, 5824, 5505, 5345, 707, 1367, 1848, 4755, 1161, 583, 1403, 6179, 6262, 5345, 191, 1028, 2044, 6459, 4356, 2759, 4871, 5608, 4074, 2004, 4432, 4758, 5748, 2510, 3719, 65, 1463, 737, 2258, 2085, 2260, 56, 1526, 4428, 4510, 4512, 4529, 5122, 5203, 5232, 5256, 4529, 74, 3721, 1614, 809, 1286, 2059, 1987, 2158, 6560, 6560, 2186, 2140, 2113, 3402, 5832, 5835, 5835, 5835, 3648, 4299, 4308, 5934, 4393, 6157, 2756, 4266, 6537, 2881, 4484, 2228, 2213, 1978, 1285, 5100, 4777, 4367, 4372, 5081, 5918, 2195, 807, 65, 791, 2166, 5650, 4516, 2833, 5534, 6498, 4830, 4431, 4513, 4756, 4686, 1295, 2005, 1987, 1835, 3860, 1619, 1772, 4434, 5483, 4671, 4430, 5122, 5128, 4675, 2262, 4439, 5937, 2232, 29, 1530, 2058, 6298, 4113, 6557, 1457, 4288, 6534, 6534, 4362, 2186, 1457, 2096, 2032, 2032, 1525, 3802, 5294, 5671, 3509, 2159, 1445, 2996, 4834, 4435, 2526, 4994, 5857, 6263, 5415, 3150, 5078, 5048, 6504, 6504, 2112, 5406, 6048, 2754, 2769, 5065, 6502, 6501, 6501, 2846, 3647, 1462, 1504, 3934, 29, 1513, 2139, 2220, 4704, 538, 305, 913, 4314, 6258, 4992, 2753, 1608, 72, 1037, 1437, 654, 2838, 5589, 5995, 2195, 71, 5906, 5986, 4439, 560, 1271, 2112, 1701, 5832, 5835, 5916, 5919, 5919, 5838, 5190, 1869, 3651, 5214, 4512, 4756, 4759, 4749, 4537, 5755, 2852, 692, 2180, 1855, 3881, 1433, 1403, 5777, 3988, 4015, 1937, 1454, 5831, 3643, 5028, 5757, 3606, 5030, 6404, 6158, 6239, 3809, 1649, 3872, 2136, 2169, 5186, 5107, 2937, 6261, 2004, 6374, 4423, 5188, 2198, 1801, 1885, 6459, 6534, 6534, 5672, 1226, 1964, 2112, 4299, 6486, 6486, 4299]\n", "2025-03-02 17:31:48,726 INFO yield speech index:0, len 11.36, rtf 0.161,  cost 1.825s,  all cost time 1.825s\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:01<00:00,  1.83s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 5 进度：1\n", "任务 5 完成，生成 1 个片段\n", "任务 4 进度：1\n", "任务 4 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:31:48,787 INFO llm job done, time cost: 1.838s\n", "2025-03-02 17:31:48,787 DEBUG speech_tokens: len: 317  data: [2112, 2028, 5832, 5916, 5943, 5832, 3645, 1542, 2112, 4675, 4528, 4447, 5409, 5179, 5176, 5907, 5178, 4567, 4736, 3590, 6533, 6560, 6560, 3395, 3799, 73, 5093, 4616, 2891, 629, 719, 2086, 4299, 2112, 2028, 3888, 5832, 5832, 5859, 5832, 3972, 4299, 4299, 5133, 4753, 135, 5086, 5095, 306, 4565, 5429, 4943, 3402, 6318, 1955, 1217, 2864, 4604, 4859, 2429, 461, 707, 716, 719, 1370, 2733, 4833, 1053, 584, 674, 6176, 6289, 6073, 2411, 113, 1586, 2073, 6537, 577, 3404, 4871, 4716, 4074, 6380, 4432, 4425, 5986, 68, 3653, 737, 801, 2219, 2237, 4509, 5482, 46, 4430, 5153, 5963, 5963, 5258, 5987, 802, 323, 2105, 1859, 2186, 4373, 5804, 4346, 2159, 2159, 2112, 4299, 4212, 3888, 5832, 5832, 5832, 5832, 5832, 3645, 1785, 2049, 5850, 4375, 5429, 2088, 6462, 4520, 4406, 2240, 4886, 4912, 5020, 6310, 5831, 4808, 5921, 4382, 1044, 2219, 79, 4200, 4678, 2834, 5452, 5529, 5151, 4753, 4433, 4677, 3238, 1295, 2753, 323, 566, 2015, 2005, 4130, 4130, 4049, 4515, 4590, 4509, 2233, 4423, 5205, 5409, 4671, 4492, 4473, 6093, 2225, 804, 1275, 5565, 6311, 5824, 3644, 6462, 6537, 6534, 4320, 1448, 1457, 1454, 1445, 1448, 1364, 2109, 4299, 4299, 2031, 1525, 3802, 5999, 4943, 4319, 665, 389, 4444, 4516, 267, 3509, 6502, 3237, 2340, 2894, 5039, 5777, 5773, 2049, 4758, 2646, 2682, 5038, 5046, 6501, 4311, 4699, 2921, 734, 1478, 3935, 317, 326, 137, 960, 2112, 57, 2274, 582, 386, 116, 1641, 4315, 4068, 5074, 5019, 1698, 66, 59, 1452, 654, 2838, 5599, 5996, 2924, 74, 6230, 74, 2324, 380, 272, 1702, 1950, 3645, 5916, 5835, 5916, 5835, 3003, 1869, 5133, 4429, 4676, 4452, 4537, 5675, 707, 1937, 1928, 2171, 1403, 5774, 6202, 4014, 4367, 3641, 5831, 5831, 4947, 5028, 5028, 2879, 5755, 5509, 5509, 6022, 1625, 1649, 2169, 2163, 677, 3647, 5111, 4396, 5857, 4074, 6378, 4680, 4406, 3650, 848, 1829, 6537, 3600, 5024, 1226, 1235, 1954, 2109, 4299, 2112, 1701, 3645]\n", "2025-03-02 17:31:49,055 INFO llm job done, time cost: 2.107s\n", "2025-03-02 17:31:49,055 DEBUG speech_tokens: len: 332  data: [2031, 1701, 3888, 5832, 5832, 3645, 3645, 3645, 3645, 2109, 6378, 4671, 4529, 5409, 5661, 5176, 5661, 4447, 4564, 5774, 6506, 4346, 1457, 2989, 5726, 6373, 2243, 5915, 5186, 4412, 4501, 5151, 1707, 3648, 3648, 3648, 5835, 5835, 3648, 2112, 4299, 4299, 4299, 5379, 5076, 4752, 4853, 2664, 540, 4537, 5509, 3404, 6318, 6318, 1949, 575, 2894, 5338, 5586, 4612, 704, 641, 2733, 4833, 2685, 665, 674, 5777, 5530, 5448, 2006, 4299, 4299, 2041, 4045, 4127, 221, 32, 776, 1477, 1722, 6537, 4347, 3485, 6320, 4961, 4960, 6255, 1887, 3462, 4513, 4434, 4776, 4445, 3719, 731, 1466, 32, 309, 74, 5158, 4834, 4428, 4511, 4393, 5365, 5391, 4527, 154, 1533, 2510, 566, 1619, 2092, 2068, 6533, 5803, 4346, 1430, 2177, 2059, 2031, 4131, 3888, 5832, 3645, 1788, 6486, 3993, 3663, 4456, 4266, 6534, 4592, 4778, 5614, 5640, 5667, 4857, 6553, 5099, 5270, 2299, 1038, 2255, 879, 4679, 2319, 2887, 6261, 5286, 4440, 4510, 4513, 2463, 3239, 3239, 1781, 1853, 1942, 4130, 4130, 1943, 4049, 1772, 1772, 4431, 4753, 4428, 2234, 4394, 5125, 4680, 4680, 4528, 2934, 6018, 2232, 2297, 802, 5649, 6300, 6553, 6560, 4288, 6534, 6535, 6507, 2158, 2159, 2159, 2186, 3644, 2112, 3888, 5835, 5835, 3645, 3645, 3645, 2112, 3690, 4421, 5509, 5047, 2888, 380, 4431, 4676, 123, 5075, 6259, 1023, 4752, 4853, 5081, 2861, 6506, 5773, 2148, 2733, 4752, 2754, 4957, 5065, 5776, 5772, 6498, 5428, 3647, 11, 3935, 3719, 290, 515, 299, 1514, 1513, 2112, 2112, 2247, 2193, 502, 305, 113, 2130, 4312, 5074, 4777, 1770, 64, 461, 1446, 651, 648, 6085, 5266, 5918, 32, 3799, 3719, 137, 2585, 623, 1244, 1948, 3888, 5832, 5832, 3645, 3648, 2112, 5133, 4510, 4512, 4525, 5509, 2870, 4124, 3880, 1928, 719, 5048, 5771, 3775, 3880, 2180, 2186, 4346, 6533, 6559, 2841, 2841, 2841, 5057, 6482, 5995, 5293, 3836, 1685, 1927, 2184, 1441, 3971, 5108, 4415, 5850, 3912, 2112, 6379, 2493, 5108, 2189, 1721, 1804, 6537, 1413, 5510, 6077, 497, 1985, 1966, 4215, 6486, 6486, 3975, 6486, 6486, 6486, 6486, 6486, 6486]\n", "2025-03-02 17:31:50,209 INFO yield speech index:0, len 12.08, rtf 0.270,  cost 3.264s,  all cost time 3.264s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:03<00:00,  3.27s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 9 进度：1\n", "任务 9 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:31:50,511 INFO yield speech index:0, len 12.20, rtf 0.301,  cost 3.674s,  all cost time 3.674s\n", "100%|██████████| 1/1 [00:03<00:00,  3.68s/it]\n", "2025-03-02 17:31:50,517 INFO yield speech index:0, len 12.24, rtf 0.292,  cost 3.580s,  all cost time 3.580s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:03<00:00,  3.58s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 1 进度：1\n", "任务 1 完成，生成 1 个片段\n", "任务 8 进度：1\n", "任务 8 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:31:50,744 INFO yield speech index:0, len 12.32, rtf 0.320,  cost 3.946s,  all cost time 3.946s\n", "100%|██████████| 1/1 [00:03<00:00,  3.95s/it]\n", "2025-03-02 17:31:50,920 INFO yield speech index:0, len 12.44, rtf 0.325,  cost 4.049s,  all cost time 4.049s\n", "\n", "100%|██████████| 1/1 [00:04<00:00,  4.05s/it]\n", "2025-03-02 17:31:50,928 INFO yield speech index:0, len 12.68, rtf 0.316,  cost 4.007s,  all cost time 4.007s\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:04<00:00,  4.01s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 0 进度：1\n", "任务 0 完成，生成 1 个片段\n", "任务 2 进度：1\n", "任务 2 完成，生成 1 个片段\n", "任务 6 进度：1\n", "任务 6 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:31:50,953 INFO yield speech index:0, len 13.28, rtf 0.306,  cost 4.066s,  all cost time 4.066s\n", "\n", "\n", "100%|██████████| 1/1 [00:04<00:00,  4.07s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 3 进度：1\n", "任务 3 完成，生成 1 个片段\n", "--- 4.196327447891235 seconds ---\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["start_time = time.time()\n", "await test_concurrent_instruct(10, semaphore_limit=10)\n", "print(\"--- %s seconds ---\" % (time.time() - start_time))"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]2025-03-02 17:31:53,420 INFO synthesis text 这是任务零，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:31:53,469 INFO synthesis text 这是任务一，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\u001b[A2025-03-02 17:31:53,489 INFO synthesis text 这是任务二，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\u001b[A\u001b[A2025-03-02 17:31:53,506 INFO synthesis text 这是任务三，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A2025-03-02 17:31:53,517 INFO synthesis text 这是任务四，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:53,529 INFO synthesis text 这是任务五，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:53,541 INFO synthesis text 这是任务六，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:53,551 INFO synthesis text 这是任务七，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:53,563 INFO synthesis text 这是任务八，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:53,576 INFO synthesis text 这是任务九，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:53,608 INFO synthesis text 这是任务十，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:53,656 INFO synthesis text 这是任务十一，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:53,692 INFO synthesis text 这是任务十二，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:53,734 INFO synthesis text 这是任务十三，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:53,765 INFO synthesis text 这是任务十四，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:53,782 INFO synthesis text 这是任务十五，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:53,797 INFO synthesis text 这是任务十六，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:53,817 INFO synthesis text 这是任务十七，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:53,839 INFO synthesis text 这是任务十八，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:31:53,878 INFO synthesis text 这是任务十九，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:31:55,024 INFO llm job done, time cost: 1.137s\n", "2025-03-02 17:31:55,025 DEBUG speech_tokens: len: 247  data: [1947, 3645, 3648, 3645, 3645, 1461, 2112, 5648, 2223, 4438, 6454, 5652, 5177, 6148, 6393, 5418, 4599, 4988, 3590, 6506, 6559, 5825, 4671, 6374, 6377, 6375, 5418, 5419, 3719, 5401, 4671, 2909, 5095, 309, 4700, 2756, 5589, 4132, 1949, 680, 5825, 5587, 2426, 725, 1289, 4752, 1305, 3590, 5774, 6046, 2672, 152, 848, 2071, 6534, 1301, 4871, 5445, 1806, 5405, 4506, 2335, 3233, 1466, 116, 297, 2243, 5400, 6380, 2223, 4439, 5122, 5448, 5418, 4448, 3800, 802, 314, 1295, 1700, 4370, 6560, 5827, 3644, 3644, 2168, 1948, 3159, 3888, 3645, 3648, 2919, 1788, 2130, 5850, 4376, 4266, 6507, 4574, 2240, 5614, 5641, 4938, 5343, 3643, 5078, 3005, 62, 73, 2336, 2013, 4676, 2238, 5075, 4803, 4426, 4510, 4431, 1052, 3482, 2105, 1943, 3725, 5482, 5645, 4448, 4393, 6097, 6381, 4501, 2367, 4393, 2243, 1776, 4920, 3393, 5824, 4373, 4269, 6534, 4359, 1451, 3875, 3883, 5999, 4871, 674, 719, 875, 4432, 4510, 294, 5534, 6262, 4917, 4772, 5075, 5044, 3480, 5805, 2766, 5047, 6501, 3583, 5105, 749, 1720, 29, 876, 60, 4704, 511, 35, 1638, 4314, 4831, 4777, 306, 137, 1362, 651, 2838, 5680, 5998, 2546, 157, 5986, 2261, 2324, 542, 1757, 1704, 3645, 5835, 5835, 5838, 3003, 1788, 4485, 4596, 2382, 4537, 5053, 3638, 1936, 1694, 677, 2861, 5777, 5774, 4015, 1937, 3641, 5101, 4947, 5028, 2770, 5759, 5751, 6238, 3809, 1679, 1676, 2163, 1431, 2999, 4388, 5937, 3471, 4672, 4537, 4379, 830, 1802, 6534, 6534, 5024, 5671, 4880, 1226, 1957, 1947, 6075, 5832, 5835, 3648, 732]\n", "2025-03-02 17:31:55,240 INFO yield speech index:0, len 9.88, rtf 0.174,  cost 1.723s,  all cost time 1.723s\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:01<00:00,  1.73s/it]\u001b[A\u001b[A\u001b[A\u001b[A\n", "2025-03-02 17:31:55,248 INFO llm job done, time cost: 1.341s\n", "2025-03-02 17:31:55,248 DEBUG speech_tokens: len: 265  data: [4131, 6075, 3645, 1785, 6378, 5652, 4527, 6373, 6374, 4446, 5176, 6384, 5176, 4564, 4979, 2861, 4319, 6533, 5804, 3644, 1457, 2177, 2012, 5166, 6211, 6374, 6377, 6374, 4194, 3719, 3721, 4191, 5405, 4435, 4650, 4940, 4534, 5158, 4915, 4853, 5077, 306, 4726, 4700, 3403, 6075, 1946, 572, 2909, 5345, 2429, 704, 635, 2018, 2139, 4299, 1950, 2112, 3435, 4833, 1062, 611, 2051, 6179, 6289, 5344, 197, 875, 2125, 4353, 4347, 3488, 4871, 4474, 3831, 5650, 4678, 4606, 3151, 731, 737, 147, 64, 2336, 6048, 5319, 4574, 6032, 5959, 5959, 6067, 5330, 1611, 1531, 1295, 1285, 1762, 2068, 4372, 6559, 4373, 1457, 1410, 3483, 6075, 3888, 5832, 5832, 2919, 1869, 2121, 4396, 4840, 2428, 6534, 3519, 4511, 4406, 4643, 4885, 4965, 5100, 4123, 3616, 5510, 4543, 147, 2246, 70, 5650, 4686, 5021, 5776, 5529, 4416, 4513, 4677, 1051, 3482, 4373, 1861, 1859, 3713, 6048, 5322, 4496, 5225, 5209, 6048, 2316, 4492, 5856, 38, 74, 3462, 4752, 5095, 3560, 2105, 6462, 6543, 2104, 2186, 2180, 3792, 5294, 5022, 3509, 692, 218, 4444, 4510, 4731, 4993, 6181, 6262, 4683, 4529, 2906, 6505, 5770, 4929, 5319, 2682, 5066, 5776, 6498, 2846, 5834, 740, 4016, 281, 113, 1842, 4461, 4866, 287, 8, 1557, 4318, 6019, 5073, 213, 63, 218, 1446, 651, 5598, 5918, 3005, 68, 4042, 2504, 2567, 551, 1975, 3645, 5832, 5835, 5835, 3003, 1788, 4513, 4678, 4534, 5509, 4949, 2909, 3872, 1928, 686, 5693, 6175, 3798, 1856, 2102, 6559, 3606, 5028, 4947, 5753, 6400, 5752, 5510, 1892, 1649, 1197, 1431, 3242, 5108, 3018, 3750, 6379, 5652, 4460, 2921, 1721, 1805, 6456, 6534, 2872, 5591, 1964, 1967, 2040]\n", "2025-03-02 17:31:55,257 INFO llm job done, time cost: 1.362s\n", "2025-03-02 17:31:55,257 DEBUG speech_tokens: len: 269  data: [3888, 5835, 5835, 5832, 1461, 4164, 5482, 2232, 4690, 4672, 2232, 3718, 5409, 4528, 5059, 5777, 6506, 4372, 3638, 879, 4920, 3138, 1133, 1352, 2168, 1448, 2953, 4833, 2565, 2891, 5092, 2664, 1761, 4537, 5752, 3491, 491, 4860, 5592, 1304, 575, 4853, 5344, 3155, 1445, 1127, 546, 5562, 1377, 593, 674, 6422, 5533, 4368, 3887, 143, 758, 1801, 4269, 6537, 686, 5594, 4964, 5446, 6018, 5649, 4673, 4677, 4534, 3070, 1046, 3647, 2195, 159, 72, 2243, 2261, 4590, 4752, 2225, 4394, 5122, 4500, 4528, 3719, 1614, 2510, 1375, 1771, 2092, 2185, 4372, 4373, 2186, 2168, 4299, 3888, 5832, 5835, 5835, 5835, 1461, 4299, 3666, 4457, 5185, 1090, 6507, 4808, 2258, 3427, 5641, 5020, 5344, 4372, 5087, 5267, 5921, 2222, 801, 2246, 1536, 5651, 4408, 2725, 3590, 5533, 4750, 4509, 4513, 2499, 5426, 4940, 4130, 3887, 1943, 1781, 4428, 4753, 2260, 4412, 4393, 4753, 4410, 4411, 3906, 4430, 317, 1776, 5565, 4122, 5825, 4373, 3550, 6507, 6507, 4323, 2186, 1373, 1454, 1451, 1606, 3883, 6239, 4946, 2861, 4346, 719, 623, 68, 4509, 4731, 2807, 6262, 4075, 4677, 2423, 5093, 5045, 4071, 4758, 4077, 2769, 5065, 6504, 6501, 5428, 3647, 47, 3962, 317, 29, 885, 1029, 4461, 2679, 539, 113, 4071, 3828, 5020, 4939, 798, 68, 389, 1452, 654, 2838, 5599, 5915, 5840, 278, 3719, 5986, 2504, 2657, 623, 1975, 1701, 5832, 5835, 5865, 5838, 3975, 4137, 5161, 4675, 2247, 4570, 5752, 5084, 4124, 3881, 1694, 707, 3590, 5045, 3745, 1586, 2180, 6560, 2877, 5028, 5028, 5756, 6482, 6239, 5996, 6023, 3836, 1440, 1431, 3728, 5837, 4379, 5856, 3832, 5650, 4446, 4486, 3647, 1883, 4251, 6534, 1440, 6482, 2675, 1235, 1948]\n", "2025-03-02 17:31:55,271 INFO llm job done, time cost: 1.374s\n", "2025-03-02 17:31:55,272 DEBUG speech_tokens: len: 271  data: [1948, 4299, 4137, 5647, 4672, 4420, 4437, 4996, 4923, 5904, 5904, 5175, 4484, 5041, 5776, 5804, 2186, 2183, 5895, 4590, 4590, 4752, 1773, 1531, 1532, 3071, 5258, 1517, 4218, 6486, 6486, 6486, 6486, 6486, 1950, 4755, 4833, 387, 5086, 315, 4519, 4700, 3403, 6318, 6318, 1229, 710, 5086, 4614, 4615, 2909, 641, 2733, 3132, 1228, 1400, 5288, 4101, 6072, 224, 842, 1847, 4353, 1413, 3485, 4880, 5446, 2121, 6379, 6377, 4425, 2344, 38, 3647, 734, 804, 2261, 2980, 4834, 4428, 4421, 4421, 4504, 5314, 6059, 3799, 76, 647, 2105, 1856, 1454, 4373, 6557, 4373, 1457, 1448, 2087, 2112, 4299, 1722, 3666, 4375, 5428, 2163, 6534, 2297, 4670, 5615, 4966, 4966, 4912, 4123, 5096, 5186, 2219, 2010, 74, 79, 2004, 4435, 2726, 5776, 4800, 4425, 5402, 5405, 4679, 2499, 4211, 1295, 2015, 2005, 2032, 1942, 3887, 1862, 5241, 4509, 4428, 2264, 4448, 4477, 4680, 4671, 4528, 2937, 5391, 65, 801, 1977, 5565, 4122, 5827, 3644, 6453, 6534, 6537, 2100, 2186, 3644, 1448, 2087, 2113, 2031, 1506, 4448, 5995, 5048, 3617, 707, 875, 4428, 4676, 2220, 4940, 6262, 1050, 2331, 2891, 5776, 5772, 546, 4590, 3240, 5066, 5772, 6258, 3583, 5915, 14, 776, 1505, 2, 147, 2139, 33, 2760, 512, 854, 4071, 4314, 4805, 2347, 1026, 29, 1119, 573, 651, 5598, 5924, 3005, 35, 1531, 6149, 2504, 110, 380, 515, 1729, 1788, 4299, 6081, 4728, 4753, 4758, 4588, 5022, 5093, 3386, 4115, 1406, 2861, 5044, 4016, 1855, 1937, 3644, 5101, 4866, 4947, 5028, 2770, 6481, 6238, 6481, 6239, 1649, 1199, 2166, 1431, 812, 2195, 5853, 1878, 4921, 2259, 4382, 2918, 2126, 6537, 6540, 1440, 6320, 2684, 1235, 1957, 2112, 4299, 6486, 6486, 4218]\n", "2025-03-02 17:31:55,361 INFO llm job done, time cost: 1.460s\n", "2025-03-02 17:31:55,362 DEBUG speech_tokens: len: 275  data: [6075, 5832, 5835, 5835, 5835, 3651, 2112, 2004, 5645, 5177, 4528, 5644, 5418, 5180, 5907, 5175, 4564, 5059, 5696, 4346, 2186, 3718, 5319, 5238, 1534, 3721, 5482, 2250, 4460, 5189, 5203, 5854, 4752, 4836, 2422, 5084, 5095, 1029, 4511, 5671, 2678, 4860, 5589, 1223, 575, 5087, 5506, 5506, 461, 1454, 1855, 5487, 1134, 593, 1403, 6178, 6315, 5345, 116, 848, 2073, 6534, 685, 5591, 5609, 5203, 3993, 5648, 4686, 4534, 3070, 3719, 3650, 2195, 960, 63, 2255, 4428, 5483, 2232, 4439, 5149, 5124, 5256, 4448, 1532, 1611, 80, 3563, 1943, 2092, 2077, 4373, 6560, 2186, 2140, 5589, 5103, 5859, 5835, 5838, 1707, 1725, 3831, 2233, 4570, 5066, 3887, 6453, 6534, 4772, 4430, 2213, 4885, 4885, 4911, 5019, 5100, 3320, 2180, 5830, 4780, 5920, 2249, 1770, 65, 76, 2004, 4675, 2482, 5047, 6505, 5532, 4413, 4590, 4593, 1050, 3482, 1133, 1943, 1943, 2105, 3715, 5322, 4593, 75, 4439, 5121, 5125, 4752, 2259, 4483, 6177, 3204, 2261, 1776, 6297, 3870, 6229, 6559, 1457, 6462, 6535, 6535, 2185, 2186, 1457, 2180, 4123, 3884, 5996, 4946, 1322, 728, 1208, 5164, 4590, 2544, 5750, 6018, 6262, 5970, 3152, 5054, 5048, 5772, 1023, 5322, 3483, 4957, 5047, 5775, 6501, 2765, 2918, 776, 3691, 74, 68, 1851, 2193, 2517, 620, 35, 928, 1887, 4317, 3828, 5100, 5020, 804, 59, 1449, 651, 2835, 5356, 5186, 4463, 35, 3799, 74, 2648, 1757, 1701, 5940, 5940, 5916, 3003, 1788, 4404, 4753, 4674, 4579, 5508, 2843, 716, 4123, 4124, 704, 5048, 5693, 3989, 4015, 2183, 5831, 2769, 4947, 4947, 5039, 6404, 6400, 6238, 3809, 1652, 1685, 2157, 2160, 2918, 4378, 3666, 1806, 6380, 2259, 4379, 2189, 1882, 4278, 6535, 6534, 3485, 2687, 1957, 4299, 4299, 4299]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 4 进度：1\n", "任务 4 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:31:55,452 INFO llm job done, time cost: 1.560s\n", "2025-03-02 17:31:55,453 DEBUG speech_tokens: len: 281  data: [4299, 6486, 4299, 2490, 2574, 4609, 5409, 4680, 3718, 3720, 5260, 4807, 5777, 5777, 1457, 2099, 1923, 4677, 4513, 4510, 4513, 4677, 5183, 5750, 5587, 1772, 2113, 4299, 3651, 5292, 5562, 117, 5087, 468, 540, 5509, 5672, 6318, 6328, 575, 710, 5342, 5341, 5078, 719, 2099, 1032, 5562, 3564, 593, 1403, 6500, 6208, 6073, 3155, 788, 1516, 1738, 6534, 6534, 2756, 4955, 5690, 6258, 4191, 6377, 4678, 4534, 157, 5987, 2, 3650, 2195, 150, 76, 140, 4428, 5319, 4590, 2306, 5306, 6043, 6061, 5324, 3719, 1530, 161, 1294, 2005, 2005, 2095, 4373, 5831, 3644, 1457, 2093, 2112, 4218, 4299, 2112, 3666, 4406, 4849, 3802, 6534, 6534, 4843, 4405, 2210, 3671, 1763, 1285, 4911, 5019, 5341, 4372, 2915, 5509, 5269, 2465, 888, 76, 59, 1689, 5649, 4432, 2834, 6263, 5529, 4750, 4429, 4432, 4676, 4678, 1052, 1295, 2105, 2095, 2059, 1861, 2105, 3482, 4431, 4509, 4752, 2261, 4421, 5206, 4428, 2241, 4519, 5856, 3666, 155, 1932, 5406, 4104, 6557, 4373, 6462, 6535, 6534, 4359, 2186, 4373, 2186, 2059, 2031, 2031, 1525, 5989, 5995, 5026, 3590, 1430, 713, 2257, 4510, 4515, 2807, 6263, 6262, 5244, 3395, 5075, 5773, 6261, 5406, 3105, 5601, 5056, 5776, 5772, 6498, 2854, 5834, 3650, 770, 3692, 3719, 29, 623, 1514, 2112, 60, 4461, 2526, 386, 59, 1641, 4073, 6501, 5291, 5073, 4939, 2753, 1932, 54, 110, 272, 551, 2059, 4299, 4299, 654, 4944, 6085, 5996, 5921, 71, 5908, 5905, 4403, 2495, 551, 1757, 4299, 2112, 5862, 4590, 4756, 2247, 4567, 5508, 5081, 4367, 3872, 686, 3590, 3989, 4097, 4367, 5101, 2760, 5028, 2769, 5756, 6158, 5509, 6022, 3863, 1676, 1685, 2160, 712, 3728, 5108, 750, 6181, 4191, 4680, 5189, 2921, 1477, 3991, 6456, 6534, 2765, 5672, 2693, 1961]\n", "2025-03-02 17:31:55,592 INFO llm job done, time cost: 1.698s\n", "2025-03-02 17:31:55,592 DEBUG speech_tokens: len: 289  data: [4299, 6486, 6486, 6486, 6486, 4299, 2031, 4672, 2502, 4843, 5176, 5644, 5419, 6148, 5661, 5418, 4484, 5042, 5776, 5803, 5831, 1453, 2166, 2160, 3566, 6077, 6086, 5429, 5429, 5438, 5518, 1711, 4215, 6486, 6486, 6486, 4299, 4299, 3675, 4482, 4753, 4509, 2657, 5095, 1110, 4681, 4780, 2759, 5589, 4141, 1952, 656, 5096, 4858, 5587, 713, 638, 1370, 1275, 5562, 3483, 593, 4319, 6179, 6315, 6074, 113, 29, 2045, 4278, 6534, 2837, 6320, 5609, 5447, 5529, 2148, 3462, 4676, 4533, 4526, 3799, 734, 2195, 75, 2261, 2980, 5726, 2493, 4736, 4960, 5365, 5418, 4528, 127, 1563, 754, 566, 1133, 1856, 2186, 4373, 5827, 5101, 3644, 2186, 2140, 2031, 3483, 1701, 6486, 6486, 6486, 4218, 4218, 3747, 2935, 5914, 5428, 4275, 6534, 2585, 4511, 4412, 4400, 4966, 4965, 4911, 5344, 5827, 4808, 5108, 148, 297, 2264, 1284, 4675, 2994, 5723, 5777, 4830, 4407, 4675, 4675, 4677, 3238, 647, 1700, 1943, 1214, 2267, 4753, 6373, 2990, 5365, 5124, 5645, 4420, 4474, 5853, 2477, 1828, 2004, 6295, 4122, 5823, 4373, 4276, 6534, 6510, 2186, 1457, 1457, 1451, 3077, 4452, 5270, 5035, 5777, 701, 389, 4434, 4756, 2499, 5021, 5209, 6181, 5415, 6310, 5090, 5045, 6178, 5484, 3564, 5048, 5043, 6501, 2854, 5348, 11, 1532, 245, 56, 1932, 1761, 2193, 501, 386, 116, 1884, 4315, 3832, 5064, 1590, 73, 388, 573, 651, 3411, 5842, 5272, 5111, 155, 6230, 308, 380, 380, 2000, 2112, 4299, 4299, 3651, 4407, 4432, 4678, 4579, 5509, 5057, 2900, 3884, 4124, 1442, 689, 5777, 5774, 3773, 1612, 1856, 2102, 1457, 5831, 2787, 5028, 2769, 5030, 6401, 6238, 6265, 6266, 1892, 1649, 1649, 3863, 2169, 2166, 712, 5915, 5111, 2938, 3832, 6379, 4689, 5269, 5837, 749, 2045, 6456, 2898, 5509, 5671, 2684, 1235, 1969, 2112, 6486, 6486, 6486, 6486, 4218]\n", "2025-03-02 17:31:55,611 INFO llm job done, time cost: 1.726s\n", "2025-03-02 17:31:55,611 DEBUG speech_tokens: len: 290  data: [2031, 2112, 2733, 4923, 4528, 4446, 5725, 4689, 2989, 6147, 5175, 4591, 5075, 6506, 6424, 6533, 1457, 2087, 1976, 5752, 6482, 1865, 1676, 1840, 1705, 5835, 5835, 5835, 5835, 5832, 3645, 1785, 5352, 4833, 2565, 5096, 477, 297, 4780, 5024, 2759, 3412, 5589, 1949, 1223, 5084, 5096, 5344, 6074, 1454, 1457, 2099, 1284, 5565, 1377, 1403, 4232, 5560, 5345, 221, 875, 2070, 6534, 2759, 2693, 4637, 5286, 1275, 4679, 4533, 5256, 5, 1463, 2276, 150, 63, 2246, 65, 4428, 5726, 5644, 4448, 4448, 5122, 5206, 5175, 4450, 76, 1563, 781, 566, 1852, 2011, 2078, 6532, 6533, 2186, 2140, 2029, 5832, 5832, 5862, 3645, 1866, 6486, 3666, 4385, 5671, 3627, 6534, 4808, 4499, 4967, 4966, 4992, 5020, 4366, 5101, 5024, 2195, 795, 68, 2267, 2013, 5648, 321, 4994, 5776, 5046, 4431, 4433, 4435, 1050, 2024, 1285, 2014, 2096, 1942, 1943, 5182, 4916, 5401, 2264, 4394, 5394, 5401, 4421, 5205, 3669, 4439, 74, 1923, 5487, 4113, 6555, 4373, 4291, 6534, 6535, 6534, 4287, 2185, 1448, 2000, 1516, 3883, 5996, 4955, 3590, 683, 68, 4509, 4759, 2806, 4724, 6502, 3237, 5256, 5078, 5039, 5777, 5773, 6258, 1032, 5481, 3564, 4957, 5047, 6505, 6504, 2854, 3647, 1460, 766, 1532, 3962, 272, 785, 1515, 2004, 6, 2760, 638, 44, 1639, 4073, 4075, 4992, 159, 78, 875, 1452, 654, 2838, 4869, 5509, 5998, 4382, 68, 1532, 5905, 56, 299, 2002, 3645, 5832, 5862, 5835, 5835, 5835, 3651, 1545, 5838, 4509, 4678, 2427, 4780, 2867, 713, 1693, 3872, 683, 2861, 5777, 6422, 1985, 1828, 4124, 2912, 5102, 2796, 5028, 3570, 5093, 6484, 6238, 5509, 6022, 3836, 1919, 2172, 2172, 893, 2918, 2228, 2937, 1644, 6380, 4447, 5188, 2, 1909, 2127, 6537, 712, 6401, 2684, 1229, 1948, 4215, 4215, 4299, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486]\n", "2025-03-02 17:31:55,661 INFO llm job done, time cost: 1.761s\n", "2025-03-02 17:31:55,662 DEBUG speech_tokens: len: 293  data: [3645, 5832, 5913, 5943, 5835, 3645, 1788, 4218, 5645, 4420, 4528, 4671, 5409, 6148, 6147, 5418, 4600, 5059, 5777, 4319, 2159, 2183, 3719, 4509, 5645, 5645, 3465, 1532, 29, 5024, 6239, 1892, 1604, 4671, 5076, 4752, 4853, 5095, 1191, 306, 4537, 6158, 572, 5589, 6328, 1304, 2894, 6313, 5586, 5339, 641, 2018, 4920, 4833, 586, 3590, 6422, 6259, 6316, 3155, 86, 1091, 1804, 6537, 3591, 4136, 4955, 4797, 1806, 4921, 4513, 4533, 4606, 1531, 3719, 731, 1463, 2195, 960, 306, 2324, 623, 1046, 4671, 6455, 2493, 4682, 4636, 5391, 5391, 5256, 4438, 1585, 1536, 79, 1295, 1862, 1856, 2183, 3644, 5830, 6559, 2915, 1447, 1215, 5832, 5940, 5832, 3645, 1458, 2112, 6015, 2206, 6157, 2539, 6507, 6534, 4493, 2228, 2483, 4885, 4884, 5019, 5344, 5824, 4372, 5078, 5918, 8, 1044, 29, 68, 2094, 4919, 2247, 5804, 5529, 4750, 5319, 5322, 5325, 3795, 4049, 1853, 1772, 1943, 3887, 1619, 4428, 5645, 2259, 4412, 5202, 4915, 4447, 4482, 6012, 2233, 29, 1773, 1977, 5811, 3384, 5823, 3644, 4279, 6534, 6537, 2186, 2183, 1613, 5898, 3074, 5998, 5681, 593, 1430, 719, 875, 4434, 4510, 2301, 5021, 6019, 6262, 4677, 1206, 2891, 5774, 5773, 2148, 4758, 3375, 2779, 5043, 5529, 6255, 2612, 5834, 731, 749, 3691, 245, 2459, 156, 2112, 2244, 4461, 582, 359, 35, 1563, 6258, 3832, 5019, 4453, 2013, 29, 380, 1443, 654, 2838, 5599, 5998, 3005, 116, 3722, 3719, 4511, 2819, 623, 1975, 1459, 3645, 5916, 5916, 5916, 3003, 2112, 4434, 4512, 4756, 4677, 4567, 5755, 2900, 3800, 1937, 1199, 2858, 6425, 4262, 1829, 1828, 4286, 6560, 5830, 5028, 5028, 2850, 5755, 5752, 5996, 3836, 1676, 2178, 2169, 1136, 5837, 2206, 5856, 1887, 6379, 5409, 4567, 4378, 1721, 1804, 6537, 6534, 4781, 5429, 3422, 1964, 2031, 2031, 4299, 4299, 4299, 6486, 6486, 4299, 4299]\n", "2025-03-02 17:31:55,716 INFO llm job done, time cost: 1.810s\n", "2025-03-02 17:31:55,716 DEBUG speech_tokens: len: 295  data: [3888, 5832, 5832, 5832, 3645, 2112, 4920, 4752, 4609, 4671, 5175, 5986, 6150, 5166, 4603, 4834, 2861, 4319, 2159, 1457, 3719, 4752, 5644, 5402, 792, 1535, 1615, 1531, 2339, 4604, 716, 2096, 2003, 3709, 4996, 4914, 2558, 5098, 477, 72, 4780, 5675, 572, 5589, 6331, 1223, 683, 2891, 3401, 5345, 5096, 725, 882, 4917, 3321, 593, 2861, 6176, 6289, 6316, 2429, 113, 1757, 2044, 6459, 4347, 575, 2777, 4961, 3831, 4191, 5405, 4677, 4857, 2344, 3719, 1463, 1466, 67, 315, 2327, 4438, 4590, 4752, 4448, 4412, 5230, 6043, 5313, 5989, 3719, 1693, 50, 566, 1133, 2090, 1454, 6560, 6557, 3644, 1457, 2140, 1945, 3645, 5832, 5835, 5835, 5838, 3648, 2112, 3747, 3663, 4570, 4850, 4266, 6543, 4592, 4751, 3455, 5614, 4912, 6073, 3640, 2914, 4780, 5191, 67, 802, 2249, 1860, 3462, 4675, 3481, 6262, 6261, 4803, 4407, 4513, 4512, 4488, 1050, 2024, 1294, 2006, 2112, 1852, 6317, 1943, 1538, 5238, 4590, 4752, 4448, 5149, 5202, 4680, 4509, 4420, 3747, 4420, 2243, 1587, 5649, 6303, 6552, 5094, 5831, 4373, 6534, 6507, 2145, 2186, 4373, 2186, 2003, 2032, 1603, 3802, 5270, 4942, 674, 2159, 1445, 2993, 4509, 4515, 3536, 6263, 3994, 4755, 1208, 2888, 5777, 6175, 3444, 5079, 2619, 4957, 5074, 4314, 6499, 6501, 5032, 5834, 1505, 1532, 32, 885, 2058, 2220, 4866, 2699, 35, 940, 4071, 6504, 6016, 4992, 160, 67, 461, 1410, 654, 2838, 4872, 5509, 5918, 2195, 3719, 5986, 317, 2324, 1271, 1999, 1701, 5832, 5835, 5835, 5835, 5838, 1059, 3894, 4485, 4432, 4759, 4587, 4780, 4945, 2816, 1451, 3881, 1199, 2861, 3590, 4097, 3799, 1612, 2180, 5831, 2913, 5028, 5028, 5074, 5755, 5752, 6238, 4052, 947, 2169, 2169, 3080, 5189, 5124, 6180, 2004, 5648, 4522, 4462, 5, 1181, 2127, 6537, 6534, 686, 4946, 506, 1964, 1967, 2032, 6486, 6486, 6486, 6486, 6486, 4299]\n", "2025-03-02 17:31:55,770 INFO llm job done, time cost: 1.889s\n", "2025-03-02 17:31:55,770 DEBUG speech_tokens: len: 300  data: [3159, 3159, 5832, 5832, 5832, 5832, 3726, 3648, 4299, 5647, 2232, 4528, 5418, 5177, 5179, 5178, 4603, 4537, 5048, 5777, 6533, 3644, 2828, 1612, 1531, 143, 728, 1889, 1808, 5936, 6178, 5130, 5319, 4590, 2585, 5084, 4851, 1032, 4511, 6158, 1298, 5589, 5589, 3406, 575, 602, 722, 4859, 5587, 2666, 710, 1448, 2093, 1932, 5001, 3321, 593, 3584, 6290, 5345, 194, 848, 1828, 4320, 3488, 4961, 3828, 5658, 4675, 5019, 4535, 3719, 731, 3653, 156, 56, 32, 4833, 4753, 2314, 4520, 5123, 5205, 5205, 4500, 2261, 1532, 1533, 890, 647, 1376, 1853, 2102, 2186, 6557, 1457, 2186, 2177, 2112, 3888, 3729, 5913, 5916, 5916, 5913, 5832, 3648, 3648, 2112, 4308, 3750, 4379, 2351, 6372, 3591, 2405, 4751, 4884, 5073, 5100, 3158, 4370, 2915, 5510, 3005, 70, 68, 68, 3462, 4686, 5669, 5777, 4803, 4431, 4673, 4686, 5669, 1376, 1943, 1943, 1862, 3724, 4590, 4671, 4529, 4394, 5125, 4671, 4528, 3018, 3666, 2261, 1851, 5730, 6066, 5826, 4373, 2102, 6534, 6535, 4368, 2105, 2105, 2186, 2177, 2009, 1606, 3874, 5267, 5048, 4319, 716, 146, 4431, 4758, 5021, 3751, 3751, 5241, 713, 5075, 5774, 6256, 5484, 3240, 2769, 5074, 6501, 6501, 3583, 5915, 2924, 751, 3668, 3665, 65, 272, 623, 1271, 1028, 1923, 60, 2274, 2760, 557, 44, 1803, 1888, 1881, 5074, 5019, 159, 63, 149, 573, 654, 4947, 4951, 5186, 2276, 62, 3800, 1531, 29, 632, 542, 2083, 1701, 5832, 5838, 5838, 5919, 5919, 1788, 3894, 4675, 5405, 4677, 4543, 6400, 2879, 725, 1694, 3881, 1190, 728, 3590, 3506, 4070, 3798, 2099, 4373, 5831, 2841, 5757, 4947, 5038, 6481, 5995, 6022, 6023, 923, 218, 2184, 2139, 1441, 5915, 5108, 4388, 4393, 5853, 5937, 6261, 4299, 6486, 2112, 3219, 4672, 4564, 5104, 749, 1562, 1804, 6540, 6534, 686, 5348, 1217, 1235, 1967, 1969, 4299, 4299, 6486, 6486, 6486, 4299, 4002, 4299, 4299]\n", "2025-03-02 17:31:55,787 INFO llm job done, time cost: 1.896s\n", "2025-03-02 17:31:55,787 DEBUG speech_tokens: len: 300  data: [5832, 5835, 5838, 5835, 5835, 3648, 4299, 5651, 2223, 4527, 5409, 5643, 5176, 3963, 5418, 4430, 4763, 5777, 6533, 3644, 3880, 3801, 4535, 4598, 704, 1373, 2018, 5130, 5725, 5645, 2260, 5095, 5829, 387, 54, 4780, 5591, 2675, 6318, 4131, 1223, 572, 5342, 5587, 470, 719, 1045, 4674, 1053, 584, 3584, 6047, 6289, 6073, 3155, 113, 848, 2046, 4269, 6534, 2756, 5594, 5608, 4554, 3993, 6351, 5405, 4407, 4750, 3070, 74, 1460, 2924, 867, 63, 2252, 4401, 4753, 2233, 4492, 4473, 5472, 5229, 4528, 2989, 3721, 80, 647, 1862, 2101, 2185, 5830, 5102, 728, 2084, 1972, 3645, 5832, 5835, 5835, 3645, 3645, 3645, 1869, 1887, 3663, 5185, 6157, 6077, 3890, 3890, 3161, 2675, 2675, 524, 6534, 6538, 6537, 2585, 4511, 38, 38, 269, 3428, 5641, 4911, 5019, 4858, 4367, 3643, 5051, 5998, 5273, 62, 1851, 63, 137, 380, 158, 1842, 5651, 42, 5102, 5532, 5532, 4416, 4756, 4432, 2490, 1781, 1295, 1295, 1943, 1915, 1700, 1619, 5170, 4833, 54, 2255, 4394, 5152, 4671, 4528, 4419, 6180, 4447, 2576, 1125, 2733, 6291, 5796, 5830, 4373, 6462, 6537, 6537, 2186, 1457, 1457, 1445, 1760, 3721, 4403, 5671, 2780, 4346, 728, 704, 149, 4431, 4515, 349, 5534, 6258, 3183, 5481, 5096, 5776, 5770, 2742, 5562, 4944, 5065, 5769, 6258, 5033, 5834, 1463, 752, 3772, 272, 29, 957, 60, 4785, 582, 395, 44, 1642, 4315, 6019, 5073, 4534, 1029, 29, 631, 573, 651, 2757, 5681, 5267, 3653, 2249, 5906, 3799, 4439, 4601, 596, 2000, 1948, 5835, 5835, 5835, 5835, 5838, 1869, 5133, 4753, 4434, 4570, 5509, 5081, 1937, 1693, 1694, 704, 1403, 6506, 5693, 3773, 1612, 1937, 2186, 3644, 2760, 5028, 5028, 2851, 6484, 6480, 5508, 5996, 3809, 1676, 1928, 2184, 2160, 2351, 5111, 2209, 6180, 6387, 5644, 4487, 2921, 830, 2047, 6534, 6537, 2898, 5752, 5591, 2693, 1235, 2029, 3645, 5838, 5838, 5838]\n", "2025-03-02 17:31:55,834 INFO llm job done, time cost: 1.930s\n", "2025-03-02 17:31:55,835 DEBUG speech_tokens: len: 302  data: [1701, 5832, 5835, 3648, 1788, 2112, 4675, 2259, 4528, 5175, 4680, 5175, 2990, 5178, 5179, 4595, 4843, 5776, 5803, 4373, 3644, 1856, 5238, 4755, 4917, 1530, 1613, 4853, 5830, 3644, 3644, 1457, 1457, 2102, 1769, 5898, 5319, 4590, 154, 2903, 5101, 387, 297, 5509, 5752, 2840, 599, 5589, 6321, 1949, 656, 2903, 5345, 4858, 2666, 710, 644, 1110, 4755, 1377, 1403, 3587, 5560, 4859, 116, 41, 1802, 6537, 685, 4946, 4883, 4797, 2013, 4676, 4443, 4525, 3719, 3647, 1463, 2195, 72, 4448, 5167, 4834, 4509, 2261, 4421, 5123, 4474, 5230, 5177, 3800, 805, 647, 1862, 2099, 5831, 6560, 725, 1408, 4299, 4299, 2112, 6015, 5122, 5914, 2593, 6534, 2629, 4403, 4670, 4965, 4992, 4615, 5827, 5830, 5051, 5998, 2222, 1047, 59, 67, 3462, 4435, 3482, 5776, 5046, 4435, 4676, 4678, 1051, 566, 1295, 1295, 1862, 1943, 4130, 3806, 4512, 4509, 4428, 4448, 4423, 5205, 4422, 4510, 4446, 4430, 5931, 3204, 56, 1854, 2814, 6294, 6228, 5824, 3644, 2101, 6534, 6534, 2182, 1457, 1457, 728, 713, 1604, 3721, 5989, 5297, 6238, 2861, 3590, 1430, 710, 1109, 4431, 4753, 4516, 294, 5075, 6259, 5532, 2742, 5328, 2894, 5048, 5773, 6261, 5730, 5562, 2754, 5075, 5047, 6504, 6258, 3582, 6158, 3656, 1460, 740, 3935, 1046, 110, 380, 299, 299, 299, 299, 1028, 793, 1869, 2112, 33, 4623, 501, 62, 1560, 1885, 4314, 5534, 4992, 4696, 1608, 56, 1117, 492, 651, 2760, 5680, 5266, 2195, 77, 6229, 2260, 4844, 2819, 623, 542, 2059, 3888, 5832, 5832, 5835, 5835, 5838, 2922, 1788, 5379, 4429, 4675, 2256, 4537, 5053, 2900, 1856, 1856, 1928, 680, 2861, 5690, 3773, 3963, 1859, 2102, 3644, 5830, 4947, 5028, 2887, 5675, 6320, 6401, 6401, 4052, 1649, 1676, 956, 2157, 2160, 3323, 5108, 2205, 5938, 4200, 6374, 4483, 5188, 11, 2126, 4275, 6534, 712, 6401, 3413, 2693, 1967, 2028, 4299, 4056, 3975, 4056, 4299]\n", "2025-03-02 17:31:55,835 INFO llm job done, time cost: 1.926s\n", "2025-03-02 17:31:55,836 DEBUG speech_tokens: len: 301  data: [6486, 4218, 4299, 3462, 5644, 5176, 5419, 6382, 5176, 5906, 6390, 6390, 4528, 4483, 2720, 5777, 6532, 5804, 5831, 4373, 1856, 5158, 6212, 6458, 6454, 4194, 5909, 888, 1275, 4435, 2591, 956, 719, 2168, 2086, 4299, 6486, 6486, 4218, 5130, 4753, 4752, 3638, 5094, 54, 5509, 6401, 3412, 6327, 1220, 575, 5087, 5587, 4616, 704, 719, 1125, 4920, 3375, 1228, 593, 5693, 5559, 6316, 224, 5, 1801, 6456, 577, 3407, 4636, 6261, 5650, 4432, 4750, 3070, 2, 3650, 229, 63, 29, 2980, 6536, 6454, 2261, 4421, 5206, 5206, 5229, 5149, 5909, 804, 2510, 1133, 2090, 2186, 6559, 6560, 2186, 2186, 2059, 4299, 6486, 6405, 6486, 6486, 6486, 6405, 4218, 3828, 2206, 4570, 3064, 6538, 3276, 4403, 4670, 5695, 3453, 4993, 5338, 5803, 5089, 4457, 147, 64, 32, 1860, 5650, 4675, 3239, 5074, 5046, 4416, 4510, 4675, 4677, 1780, 1052, 1295, 2015, 2032, 1987, 4292, 4048, 1862, 2986, 4753, 5402, 2232, 5177, 5202, 4680, 4680, 4438, 3750, 4420, 2486, 1533, 5001, 3384, 6310, 5747, 1457, 6462, 6534, 6534, 4359, 2102, 3644, 725, 2000, 1525, 5989, 5996, 5591, 3590, 674, 677, 2258, 4591, 4516, 376, 5453, 6262, 564, 4518, 2900, 5075, 5047, 4317, 2733, 4834, 2538, 4872, 5062, 5775, 6501, 5032, 6157, 2, 3800, 2, 148, 1842, 4488, 4947, 539, 143, 1884, 4314, 5534, 5100, 879, 2252, 461, 1437, 654, 2838, 4947, 5680, 5996, 5921, 62, 5906, 3718, 2243, 2585, 641, 515, 2056, 3888, 5832, 5859, 5859, 5859, 3651, 1950, 4404, 5242, 4675, 4606, 5752, 5084, 713, 3872, 1685, 1190, 710, 2861, 5695, 4227, 1960, 1960, 1749, 4043, 2102, 4373, 5831, 5028, 5028, 2841, 2843, 5753, 6238, 4052, 1649, 1189, 2166, 703, 5837, 4388, 5853, 4074, 4191, 5645, 4520, 4456, 20, 1721, 6453, 6540, 6534, 5024, 5594, 509, 509, 4215, 6486, 6486, 6486, 4299, 6486, 6486, 6405, 4218, 4218, 6486, 4299, 4218, 4218]\n", "2025-03-02 17:31:55,982 INFO yield speech index:0, len 10.60, rtf 0.204,  cost 2.165s,  all cost time 2.165s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:02<00:00,  2.17s/it]\u001b[A\n", "2025-03-02 17:31:56,084 INFO llm job done, time cost: 2.176s\n", "2025-03-02 17:31:56,085 DEBUG speech_tokens: len: 317  data: [1950, 3645, 5832, 5832, 3675, 3645, 3645, 3645, 3888, 1458, 2112, 6378, 6374, 4689, 4518, 4448, 5400, 5402, 5400, 6150, 5904, 4528, 4520, 5069, 6506, 3644, 1454, 3709, 5319, 4590, 4509, 2979, 1535, 1692, 2148, 2139, 703, 6158, 6086, 6166, 6238, 6077, 1729, 2031, 4299, 1729, 3186, 5562, 5562, 4752, 389, 2906, 5095, 1029, 4484, 6401, 1298, 5589, 6321, 2033, 656, 2909, 4613, 2909, 632, 2742, 3375, 590, 674, 5450, 6317, 971, 143, 32, 1090, 6534, 2756, 4943, 4880, 3750, 5659, 4676, 5019, 4526, 802, 1544, 1463, 2195, 72, 2261, 2261, 4509, 4833, 4752, 2260, 4520, 5231, 5229, 5313, 5257, 3799, 3800, 1533, 2510, 1268, 1619, 2183, 4373, 5828, 5826, 6560, 2186, 2176, 2028, 3645, 5832, 5862, 5835, 5106, 3648, 1461, 1785, 6486, 6486, 6486, 6486, 6486, 6486, 4299, 4299, 4299, 1716, 6015, 4385, 6076, 2432, 6534, 4320, 2216, 4427, 5614, 5694, 4992, 4615, 3640, 5099, 5267, 2924, 148, 801, 2255, 1122, 4922, 2247, 5101, 6505, 4803, 4443, 4509, 4513, 4677, 3482, 3482, 1943, 1700, 3722, 5319, 5322, 2322, 4529, 4477, 5152, 4590, 2322, 4501, 5121, 4420, 4448, 1776, 6297, 6294, 6067, 5825, 1457, 4288, 6534, 6534, 2145, 2186, 1454, 4124, 6070, 5297, 6481, 5048, 1430, 716, 2267, 4590, 2301, 5750, 5209, 3994, 5247, 2657, 5072, 5773, 6144, 5562, 3564, 5065, 3585, 6501, 2855, 5915, 3650, 884, 3962, 1046, 110, 380, 380, 623, 56, 2139, 2220, 4704, 2769, 2573, 35, 913, 4074, 4314, 5317, 5019, 79, 1029, 59, 56, 389, 308, 2000, 2002, 4299, 4299, 681, 2838, 2757, 5437, 5918, 89, 143, 803, 3961, 2503, 380, 389, 308, 2028, 1701, 5832, 3648, 5835, 5838, 1464, 2949, 5214, 4675, 4515, 4549, 5672, 707, 1694, 4123, 713, 2861, 5690, 3772, 3881, 5828, 3643, 5028, 5028, 4947, 5683, 5752, 5510, 3836, 6053, 2413, 2175, 460, 5188, 4414, 5853, 6261, 6387, 5645, 4450, 4456, 2, 830, 1801, 6456, 6453, 2513, 6401, 2684, 509, 1958, 2031, 3645, 5835, 5835, 5835, 3648, 735]\n", "2025-03-02 17:31:56,102 INFO llm job done, time cost: 2.217s\n", "2025-03-02 17:31:56,103 DEBUG speech_tokens: len: 319  data: [2031, 5832, 5832, 5859, 3645, 2112, 4299, 4191, 6131, 4447, 4528, 5167, 5726, 6374, 6390, 5176, 803, 1523, 1516, 2112, 4299, 4299, 1525, 5908, 5330, 2855, 2051, 2132, 2183, 2980, 6454, 6454, 4194, 56, 5111, 4379, 4411, 6174, 3993, 4299, 3894, 5916, 5835, 5835, 1542, 3894, 4833, 4833, 2638, 5014, 1923, 4429, 5508, 3404, 5589, 1229, 710, 5095, 4858, 4616, 623, 641, 2742, 2619, 589, 1403, 5774, 6262, 5587, 221, 1118, 2071, 6456, 1441, 6401, 4879, 5202, 1806, 4676, 4506, 4768, 965, 803, 1466, 35, 309, 2261, 2252, 5483, 5645, 2259, 4439, 5126, 5203, 5310, 5989, 2264, 1531, 3718, 1531, 647, 1853, 2140, 2096, 3644, 5827, 6533, 2159, 2159, 2122, 2031, 3645, 5832, 5832, 5832, 5832, 3645, 1458, 2112, 4299, 5931, 5850, 4699, 2873, 6534, 4356, 4844, 4445, 557, 1258, 5073, 4857, 6553, 3643, 5024, 5188, 2249, 315, 56, 1536, 6387, 5405, 322, 4967, 6259, 5529, 4443, 5483, 4433, 4678, 1051, 3482, 323, 1043, 1763, 4299, 2031, 2104, 4130, 4130, 2996, 4512, 4753, 2250, 2261, 4394, 5206, 4680, 4680, 4439, 2206, 5937, 4419, 56, 801, 1248, 5568, 4122, 6555, 5828, 2186, 6462, 6534, 6534, 6534, 2105, 2156, 2156, 1457, 2093, 2005, 1744, 3720, 4520, 5185, 5047, 4319, 1445, 2996, 4591, 4407, 2753, 4480, 6261, 2499, 3144, 2900, 5048, 5043, 4071, 4677, 3375, 4963, 5074, 5772, 5041, 5915, 14, 3962, 2189, 156, 2058, 4435, 4461, 2679, 530, 773, 1804, 4315, 4068, 5073, 4939, 1617, 1272, 59, 1271, 2167, 1302, 651, 2838, 5680, 5189, 2195, 3722, 3718, 137, 1127, 5173, 4753, 4679, 2319, 5266, 5027, 710, 1937, 3880, 3872, 680, 674, 4235, 6421, 4299, 1944, 6075, 5346, 3888, 5832, 3888, 3726, 1782, 2112, 1639, 1693, 1451, 3644, 5830, 2806, 5028, 4947, 4954, 5753, 6239, 6023, 6023, 6023, 6050, 6059, 3385, 2166, 2160, 4781, 5834, 5189, 4468, 5850, 5937, 2067, 6458, 2232, 4540, 2189, 1721, 1801, 6534, 6534, 711, 4781, 6158, 1226, 506, 1957, 2112, 2112, 1458, 5835, 5835, 5832, 3645, 1545]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 17 进度：1\n", "任务 17 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:31:56,221 INFO llm job done, time cost: 2.332s\n", "2025-03-02 17:31:56,222 DEBUG speech_tokens: len: 326  data: [2031, 1945, 4215, 4218, 4218, 5832, 3648, 2112, 5649, 4672, 4447, 4438, 4996, 4923, 5177, 5178, 5178, 4603, 4564, 5072, 5696, 4319, 4265, 2078, 2105, 2102, 2078, 2078, 2078, 2105, 2096, 2093, 3672, 4590, 5481, 4671, 2252, 2897, 5094, 1842, 4484, 5509, 3485, 5589, 6318, 2033, 656, 5090, 5345, 5507, 2414, 707, 719, 1448, 2012, 2085, 5001, 5562, 567, 674, 2048, 6256, 6261, 6072, 2420, 35, 1766, 2124, 6459, 4347, 4214, 5591, 4880, 5527, 1824, 5651, 4434, 4696, 3073, 3719, 1460, 3653, 2222, 1032, 56, 56, 4509, 4510, 4509, 2237, 4393, 5124, 4422, 4449, 2341, 3799, 802, 566, 1294, 1771, 2101, 4372, 6556, 5828, 4373, 2186, 2104, 2139, 4299, 4299, 4299, 4299, 6486, 6486, 4299, 4299, 4218, 2031, 5931, 2934, 5914, 6077, 2756, 6456, 6462, 2567, 4403, 2237, 2213, 1241, 2015, 3454, 5046, 5073, 3803, 3616, 5081, 5266, 4544, 71, 1272, 2243, 65, 1923, 4191, 5405, 294, 5696, 6258, 5529, 4749, 4485, 4759, 4651, 2715, 6398, 2753, 1376, 1943, 1862, 1862, 1861, 1781, 4512, 4672, 4431, 68, 4393, 5286, 5409, 5409, 4447, 5931, 5121, 2243, 1775, 2013, 5562, 3312, 5823, 5747, 4373, 4278, 6534, 6534, 2184, 2186, 2186, 1457, 1457, 3644, 5830, 4299, 6486, 1950, 1788, 1507, 5908, 5998, 5600, 3509, 728, 461, 71, 4593, 4597, 4758, 4913, 6262, 6261, 1806, 4755, 2584, 5090, 5048, 5773, 3585, 4920, 5562, 3240, 5039, 2860, 6501, 3583, 5834, 740, 1505, 1019, 29, 1596, 1923, 2220, 4947, 619, 314, 35, 1557, 4315, 5534, 5073, 2346, 63, 137, 1443, 651, 2838, 5589, 5914, 5269, 2195, 803, 3962, 3476, 2504, 632, 542, 623, 2056, 4299, 1704, 3645, 3645, 3648, 5916, 3729, 2112, 3894, 5217, 4515, 4512, 4677, 4849, 5915, 2843, 710, 1451, 3799, 3881, 704, 2861, 3509, 6422, 6176, 3773, 1775, 5828, 2886, 2841, 5028, 2850, 5083, 5756, 6481, 6237, 6238, 1622, 1652, 947, 2172, 2160, 2999, 5108, 2201, 3666, 6018, 4191, 6373, 4483, 5836, 11, 1721, 1804, 6537, 6537, 711, 6401, 5600, 1229, 1963, 2040, 4299, 2031, 1944, 1701, 4299, 4299]\n", "2025-03-02 17:31:56,239 INFO llm job done, time cost: 2.336s\n", "2025-03-02 17:31:56,240 DEBUG speech_tokens: len: 326  data: [4299, 1701, 3726, 5916, 5835, 3732, 1869, 4191, 5400, 4447, 4446, 6455, 5652, 5175, 5149, 5418, 4520, 4834, 5693, 6424, 6451, 5803, 5804, 3644, 1457, 2096, 2112, 2112, 2031, 5133, 5644, 5401, 6373, 3961, 6149, 5401, 6211, 6381, 5256, 4447, 1532, 1765, 2112, 2112, 3648, 3972, 5916, 5916, 5835, 5835, 5919, 1626, 3678, 5401, 4672, 2333, 5078, 477, 2251, 5266, 5753, 2683, 6075, 4132, 1223, 575, 5087, 5341, 5342, 704, 728, 1369, 5487, 3375, 2689, 1403, 3590, 5554, 4101, 5344, 116, 767, 1720, 6537, 6534, 569, 6323, 2693, 6338, 4074, 3462, 4432, 4857, 4525, 3799, 5, 3647, 2192, 300, 2255, 65, 5167, 4590, 5319, 4509, 4448, 4403, 5123, 5206, 5314, 5314, 5257, 4448, 3719, 1531, 323, 1295, 1861, 2098, 4373, 4373, 2186, 2096, 4299, 4299, 4299, 1803, 3664, 5915, 5186, 6453, 6537, 4763, 4379, 2237, 4967, 5695, 4911, 5019, 5338, 3640, 5051, 5918, 2276, 69, 68, 77, 2085, 4675, 2266, 5777, 6505, 5529, 4434, 4513, 4516, 4488, 2508, 4211, 1294, 2005, 1951, 1285, 1942, 3887, 4130, 1862, 3715, 4590, 4753, 4509, 4529, 4439, 5123, 5124, 5401, 4680, 4433, 4392, 6099, 2252, 389, 1923, 5487, 4113, 6553, 6560, 4288, 6507, 6507, 4332, 2105, 2159, 2186, 3644, 2139, 1702, 3726, 5832, 5835, 3645, 1543, 1626, 6073, 5300, 5509, 2780, 3590, 692, 119, 4428, 4675, 2482, 4966, 4075, 5241, 3638, 5048, 5041, 1770, 5562, 3483, 4958, 5047, 5772, 6498, 5429, 3647, 857, 3692, 3962, 29, 56, 2139, 30, 4623, 501, 557, 8, 40, 4071, 4068, 4832, 4965, 2347, 72, 218, 1446, 654, 2841, 5601, 5753, 5995, 2519, 152, 5909, 5985, 4430, 2657, 470, 1514, 2031, 1707, 5913, 5832, 5916, 3648, 1545, 3894, 4485, 4918, 2346, 4537, 5026, 2894, 4124, 4042, 1694, 1190, 728, 1403, 5777, 3988, 4014, 2180, 2102, 6559, 2769, 5028, 5028, 2914, 5029, 6404, 6401, 6238, 4052, 2108, 1919, 3863, 1449, 2163, 434, 5834, 5108, 2234, 5934, 4074, 6379, 4689, 4540, 2921, 776, 1481, 6453, 6534, 2900, 5591, 506, 1238, 1957, 2112, 1701, 3726, 5913, 5916, 1539]\n", "2025-03-02 17:31:56,425 INFO yield speech index:0, len 10.76, rtf 0.265,  cost 2.849s,  all cost time 2.849s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:02<00:00,  2.85s/it]\n", "2025-03-02 17:31:56,471 INFO llm job done, time cost: 2.589s\n", "2025-03-02 17:31:56,472 DEBUG speech_tokens: len: 340  data: [2112, 1701, 5832, 5835, 5919, 5835, 5835, 3729, 4056, 4299, 1977, 4675, 2342, 4439, 4753, 2988, 3721, 5907, 5179, 4568, 2855, 6506, 5777, 5804, 4367, 3401, 1294, 3453, 3481, 4940, 4940, 4697, 4750, 3256, 1707, 5109, 5835, 5835, 3648, 4299, 4218, 5376, 5726, 5645, 2250, 4843, 5827, 2340, 546, 4483, 5752, 5024, 575, 4860, 4860, 3414, 1304, 719, 5342, 5587, 4601, 713, 1451, 546, 2403, 1234, 3590, 6260, 6316, 5345, 194, 119, 1073, 6462, 6534, 578, 4865, 4879, 3828, 3462, 4678, 4857, 2338, 3233, 731, 1466, 35, 147, 1272, 137, 272, 596, 596, 596, 1244, 2029, 4215, 4299, 5133, 4752, 5483, 2241, 4439, 4394, 5123, 5206, 5314, 4532, 2993, 3799, 75, 1376, 2105, 1843, 2068, 6559, 6559, 5804, 1430, 2159, 2059, 2032, 4299, 4218, 3888, 5832, 5832, 5832, 5832, 5835, 5835, 3648, 4299, 4299, 3747, 3663, 5995, 6157, 2513, 6453, 6507, 2630, 4514, 4670, 4885, 5613, 4992, 5586, 4366, 5101, 4537, 4382, 876, 63, 59, 1608, 2004, 5404, 2510, 5047, 5533, 4803, 4443, 4753, 4513, 2490, 1052, 1295, 2015, 2096, 1943, 1862, 5173, 4752, 4914, 4448, 4421, 5124, 5394, 5645, 4392, 4392, 5937, 4420, 137, 802, 2166, 4758, 4116, 5824, 5099, 4373, 6426, 6534, 6534, 2145, 1457, 1454, 3614, 2075, 2041, 4299, 4299, 3888, 5835, 5835, 5835, 5835, 5838, 3651, 4299, 4218, 3721, 2255, 5269, 5023, 5777, 3617, 719, 596, 71, 4753, 5405, 2220, 5020, 6181, 5529, 3993, 4674, 4853, 5066, 6505, 5772, 4074, 5001, 4833, 2682, 5038, 5776, 6504, 3583, 6157, 1463, 1505, 3962, 281, 245, 65, 2139, 786, 4704, 2697, 305, 116, 1884, 4314, 3104, 5019, 159, 306, 140, 713, 1383, 651, 2766, 5681, 5996, 737, 68, 5986, 2252, 4484, 380, 380, 1972, 4299, 4299, 6486, 6486, 4137, 5832, 5832, 5835, 5835, 5835, 5835, 4299, 4137, 4404, 4513, 4678, 4777, 6238, 2867, 641, 3800, 4043, 380, 698, 3509, 3503, 3911, 1750, 2099, 6560, 3643, 2760, 2841, 4966, 5755, 5751, 6238, 3809, 1625, 923, 1188, 2160, 326, 5108, 2937, 5938, 6378, 2259, 5188, 2999, 1154, 1805, 6456, 6534, 2513, 6401, 3404, 506, 1958, 4299, 4134, 3888, 5832, 5835, 5832]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 9 进度：1\n", "任务 9 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:31:56,681 INFO yield speech index:0, len 10.84, rtf 0.284,  cost 3.073s,  all cost time 3.073s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:03<00:00,  3.08s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 10 进度：1\n", "任务 10 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:31:56,913 INFO llm job done, time cost: 3.015s\n", "2025-03-02 17:31:56,914 DEBUG speech_tokens: len: 365  data: [1948, 4299, 4299, 4299, 3888, 3969, 3888, 3969, 4299, 4299, 3975, 2031, 4299, 4299, 4299, 4299, 4299, 4218, 4218, 4299, 4299, 4299, 1842, 4752, 4771, 4438, 4753, 4446, 5260, 5907, 5166, 4600, 5062, 5777, 6506, 3641, 2981, 5562, 4590, 4752, 801, 806, 2996, 5750, 4940, 5020, 4857, 1286, 2059, 2112, 2031, 2112, 2112, 3648, 5238, 4753, 2484, 4754, 5086, 1110, 4429, 5509, 3404, 1217, 5589, 4141, 1220, 683, 5582, 5588, 2426, 719, 641, 546, 2646, 3411, 593, 5447, 6289, 6316, 230, 137, 1802, 6534, 3627, 2756, 4637, 5202, 1887, 6380, 4440, 4857, 2249, 3800, 74, 1466, 2222, 1032, 65, 65, 4509, 4753, 2322, 4493, 4394, 5206, 5988, 5338, 2341, 5905, 801, 404, 1375, 1849, 2096, 4373, 5827, 5828, 2186, 2177, 2086, 2112, 4218, 4215, 4299, 4299, 4299, 4218, 3888, 3888, 5835, 3645, 3648, 1869, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 4218, 4299, 4299, 2040, 3669, 4393, 6077, 3196, 6534, 3519, 4403, 26, 4157, 4885, 4992, 4857, 6553, 5827, 4808, 4463, 160, 73, 62, 1779, 5648, 2229, 5021, 6181, 5532, 4443, 4673, 4432, 4677, 3237, 323, 323, 1286, 1987, 2032, 2068, 4130, 1862, 1538, 5322, 4593, 2244, 4496, 4477, 4509, 4428, 4527, 5121, 6093, 29, 802, 2085, 4836, 4122, 5095, 1457, 4275, 6534, 6507, 6534, 2155, 2186, 2186, 3644, 2912, 1367, 4299, 4299, 4215, 4212, 3888, 3645, 3645, 3645, 3972, 2031, 2031, 2112, 3891, 5940, 3792, 4541, 5023, 2861, 665, 794, 4428, 4676, 285, 4913, 6261, 1023, 5562, 2666, 5075, 5773, 4071, 2733, 3375, 3411, 5038, 5775, 6501, 3582, 6157, 731, 1505, 1019, 29, 1920, 57, 4704, 2688, 557, 62, 1557, 2128, 4069, 5101, 5019, 1689, 57, 59, 1359, 654, 2838, 4951, 5915, 3005, 143, 3800, 6229, 2260, 2324, 542, 1271, 1944, 2028, 4299, 4299, 4299, 4299, 4299, 4218, 3975, 2112, 4218, 3975, 3891, 3891, 3648, 5835, 5835, 3648, 3648, 3651, 1788, 4299, 3648, 5106, 4432, 4677, 4598, 5752, 5090, 1694, 3802, 1190, 683, 3506, 3773, 3717, 1694, 1373, 1457, 5102, 4947, 5028, 4875, 6401, 5509, 5509, 3836, 1649, 1676, 1198, 2166, 461, 5837, 4379, 2935, 6180, 2085, 5648, 4450, 5917, 92, 1721, 3924, 6534, 3591, 5752, 6400, 3422, 1964, 1960, 2032, 2031, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 3975, 3975, 4299, 4299, 4299]\n", "2025-03-02 17:31:56,918 INFO yield speech index:0, len 11.00, rtf 0.289,  cost 3.184s,  all cost time 3.184s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:03<00:00,  3.19s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 13 进度：1\n", "任务 13 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:31:57,253 INFO yield speech index:0, len 11.24, rtf 0.329,  cost 3.701s,  all cost time 3.701s\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:03<00:00,  3.70s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：1\n", "任务 7 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:31:57,868 INFO yield speech index:0, len 11.56, rtf 0.372,  cost 4.305s,  all cost time 4.305s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:04<00:00,  4.31s/it]\n", "2025-03-02 17:31:58,021 INFO yield speech index:0, len 11.60, rtf 0.391,  cost 4.532s,  all cost time 4.532s\n", "\n", "100%|██████████| 1/1 [00:04<00:00,  4.54s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 8 进度：1\n", "任务 8 完成，生成 1 个片段\n", "任务 2 进度：1\n", "任务 2 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:31:58,080 INFO yield speech index:0, len 11.72, rtf 0.374,  cost 4.388s,  all cost time 4.388s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:04<00:00,  4.39s/it]\n", "2025-03-02 17:31:58,275 INFO yield speech index:0, len 11.80, rtf 0.379,  cost 4.478s,  all cost time 4.478s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:04<00:00,  4.48s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 12 进度：1\n", "任务 12 完成，生成 1 个片段\n", "任务 16 进度：1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:31:58,292 INFO yield speech index:0, len 12.00, rtf 0.406,  cost 4.872s,  all cost time 4.872s\n", "100%|██████████| 1/1 [00:04<00:00,  4.88s/it]\n", "2025-03-02 17:31:58,484 INFO yield speech index:0, len 12.00, rtf 0.412,  cost 4.943s,  all cost time 4.943s\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:04<00:00,  4.95s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 16 完成，生成 1 个片段\n", "任务 0 进度：1\n", "任务 0 完成，生成 1 个片段\n", "任务 6 进度：1\n", "任务 6 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:31:59,291 INFO yield speech index:0, len 12.08, rtf 0.456,  cost 5.509s,  all cost time 5.509s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:05<00:00,  5.51s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 15 进度：1\n", "任务 15 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:31:59,545 INFO yield speech index:0, len 12.04, rtf 0.471,  cost 5.667s,  all cost time 5.667s\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:05<00:00,  5.67s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 19 进度：1\n", "任务 19 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:31:59,751 INFO yield speech index:0, len 12.68, rtf 0.466,  cost 5.912s,  all cost time 5.912s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:05<00:00,  5.92s/it]\u001b[A\u001b[A\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 18 进度：1\n", "任务 18 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:00,098 INFO yield speech index:0, len 12.76, rtf 0.517,  cost 6.592s,  all cost time 6.592s\n", "\n", "\n", "100%|██████████| 1/1 [00:06<00:00,  6.59s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 3 进度：1\n", "任务 3 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:00,457 INFO yield speech index:0, len 13.04, rtf 0.531,  cost 6.928s,  all cost time 6.928s\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:06<00:00,  6.94s/it]\n", "2025-03-02 17:32:00,523 INFO yield speech index:0, len 13.04, rtf 0.518,  cost 6.758s,  all cost time 6.758s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:06<00:00,  6.77s/it]\n", "2025-03-02 17:32:00,660 INFO yield speech index:0, len 13.60, rtf 0.529,  cost 7.191s,  all cost time 7.191s\n", "\n", "100%|██████████| 1/1 [00:07<00:00,  7.19s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 5 进度：1\n", "任务 5 完成，生成 1 个片段\n", "任务 14 进度：1\n", "任务 14 完成，生成 1 个片段\n", "任务 1 进度：1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1/1 [00:07<00:00,  7.20s/it]\n", "2025-03-02 17:32:00,685 INFO yield speech index:0, len 14.60, rtf 0.481,  cost 7.029s,  all cost time 7.029s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:07<00:00,  7.04s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 1 完成，生成 1 个片段\n", "任务 11 进度：1\n", "任务 11 完成，生成 1 个片段\n", "--- 7.343711853027344 seconds ---\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["start_time = time.time()\n", "await test_concurrent_instruct(20, semaphore_limit=20)\n", "print(\"--- %s seconds ---\" % (time.time() - start_time))"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]2025-03-02 17:32:34,766 INFO synthesis text 这是任务零，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:32:34,800 INFO synthesis text 这是任务一，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\u001b[A2025-03-02 17:32:34,834 INFO synthesis text 这是任务二，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\u001b[A\u001b[A2025-03-02 17:32:34,868 INFO synthesis text 这是任务三，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A2025-03-02 17:32:34,903 INFO synthesis text 这是任务四，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:34,935 INFO synthesis text 这是任务五，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:34,971 INFO synthesis text 这是任务六，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:35,006 INFO synthesis text 这是任务七，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:35,041 INFO synthesis text 这是任务八，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:35,077 INFO synthesis text 这是任务九，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:35,112 INFO synthesis text 这是任务十，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:35,147 INFO synthesis text 这是任务十一，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:35,186 INFO synthesis text 这是任务十二，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:35,222 INFO synthesis text 这是任务十三，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:35,261 INFO synthesis text 这是任务十四，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:35,297 INFO synthesis text 这是任务十五，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:35,334 INFO synthesis text 这是任务十六，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:35,370 INFO synthesis text 这是任务十七，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:35,406 INFO synthesis text 这是任务十八，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:35,440 INFO synthesis text 这是任务十九，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:32:35,466 INFO synthesis text 这是任务二十，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:32:35,488 INFO synthesis text 这是任务二十一，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:32:35,506 INFO synthesis text 这是任务二十二，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:32:35,519 INFO synthesis text 这是任务二十三，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:32:35,531 INFO synthesis text 这是任务二十四，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:32:36,725 INFO llm job done, time cost: 1.175s\n", "2025-03-02 17:32:36,726 DEBUG speech_tokens: len: 250  data: [2112, 2112, 4675, 2304, 4528, 4671, 5904, 3718, 5907, 4603, 5050, 3590, 4319, 728, 1250, 5023, 5752, 3863, 3713, 5319, 5238, 5238, 2979, 1523, 2990, 4428, 4509, 2549, 5009, 390, 4438, 5428, 4946, 3403, 6318, 6318, 1304, 575, 5096, 5749, 3158, 704, 719, 2093, 1761, 5565, 1296, 2771, 1400, 6178, 5559, 3887, 194, 29, 1829, 6537, 6537, 5672, 4871, 4964, 5041, 2148, 3462, 4675, 4776, 4688, 805, 1460, 1463, 147, 2243, 59, 4509, 4671, 4421, 4421, 4421, 4502, 5230, 5959, 3771, 2510, 1294, 1700, 2182, 4373, 4373, 2186, 2177, 4299, 6486, 4227, 3747, 4384, 5834, 4266, 6534, 2657, 4499, 4886, 5614, 5641, 4939, 4123, 2908, 5998, 2195, 72, 62, 1851, 4921, 4686, 5669, 5776, 4830, 4509, 4512, 4431, 3239, 2753, 2105, 1940, 1943, 5173, 4590, 2241, 2252, 4394, 4419, 4833, 4448, 3015, 5364, 4430, 801, 3462, 6294, 6553, 5831, 4289, 6534, 6535, 4323, 2105, 2186, 2093, 1609, 3878, 5995, 4955, 2132, 683, 878, 4512, 4593, 2562, 5533, 6259, 2985, 3141, 5081, 5048, 5773, 4071, 2733, 6294, 4293, 2859, 5047, 6501, 4311, 3890, 3650, 41, 3935, 281, 245, 515, 1729, 2112, 789, 2193, 2760, 2753, 8, 847, 2127, 4311, 5290, 4992, 4940, 1680, 67, 29, 299, 1028, 2139, 654, 651, 2682, 5186, 2924, 35, 2990, 3232, 2324, 632, 1757, 1519, 5136, 5322, 4597, 2328, 5266, 5056, 2900, 3880, 1685, 674, 5771, 3773, 1829, 1454, 2915, 4947, 5757, 4947, 5756, 6239, 5995, 3836, 1676, 2160, 2139, 470, 5837, 4388, 3750, 3462, 4432, 4463, 2921, 1505, 1747, 6534, 6534, 2765, 3404, 2696, 1958]\n", "2025-03-02 17:32:36,851 INFO llm job done, time cost: 1.308s\n", "2025-03-02 17:32:36,852 DEBUG speech_tokens: len: 261  data: [2028, 4299, 6486, 6486, 6486, 4299, 4299, 1761, 4672, 4501, 4520, 4671, 4915, 4447, 3722, 2991, 5260, 4600, 5045, 3590, 4319, 3644, 3395, 3239, 5641, 5668, 5669, 5426, 4778, 4831, 3985, 4299, 6486, 6486, 4299, 4218, 5106, 4725, 4590, 370, 5081, 4770, 549, 4538, 3485, 491, 5589, 1958, 575, 5078, 4612, 3155, 623, 387, 4755, 1305, 503, 3587, 5527, 5560, 5345, 233, 116, 1091, 6537, 4347, 3491, 4964, 4555, 1887, 5407, 4435, 4749, 2335, 803, 1463, 734, 802, 2219, 4441, 4591, 4509, 4502, 4420, 5122, 5233, 4527, 4529, 883, 1614, 404, 1619, 2099, 4373, 6560, 4373, 2186, 2186, 2095, 4299, 4299, 4299, 2112, 3747, 4393, 5914, 2513, 6537, 6543, 4727, 2213, 4967, 4992, 4992, 6070, 5101, 4780, 2192, 803, 35, 150, 5650, 2508, 5021, 5776, 4803, 4416, 4510, 4432, 4677, 1052, 647, 4373, 4130, 1700, 1619, 4512, 4593, 147, 4520, 5126, 4428, 4509, 4501, 3015, 3906, 299, 1116, 4758, 5580, 5097, 728, 6453, 6534, 6537, 2105, 635, 1604, 3799, 6026, 6238, 5039, 4346, 728, 704, 2986, 4752, 2247, 5102, 5452, 6259, 4674, 3314, 5102, 5068, 1041, 4833, 2754, 4956, 5064, 6501, 5041, 5834, 11, 3962, 29, 59, 2166, 33, 2760, 512, 854, 4311, 6016, 5073, 2427, 72, 113, 1434, 651, 2754, 6167, 5918, 8, 803, 6229, 2252, 2243, 272, 1757, 1732, 5136, 5242, 2220, 4540, 5024, 3881, 4124, 1919, 2858, 5777, 3584, 1829, 1451, 2915, 4947, 4947, 4958, 5751, 5752, 3809, 1652, 1679, 3863, 2166, 703, 5837, 4463, 5937, 2094, 6373, 4486, 2192, 1829, 2073, 6534, 3627, 5023, 6320, 1235, 1963, 4218, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 4299]\n", "2025-03-02 17:32:36,953 INFO llm job done, time cost: 1.407s\n", "2025-03-02 17:32:36,953 DEBUG speech_tokens: len: 267  data: [2040, 2004, 4923, 4852, 4691, 4996, 5644, 6148, 6390, 5177, 4511, 5044, 5777, 5803, 4370, 1614, 1275, 4755, 970, 470, 1367, 2084, 3672, 4833, 4671, 2558, 5005, 1923, 2242, 6238, 3404, 5589, 4142, 1304, 2894, 5342, 5344, 2666, 1454, 882, 4836, 3483, 1403, 4316, 6043, 6315, 2411, 785, 2098, 6462, 4356, 5678, 4880, 6258, 1806, 4921, 4443, 4858, 3073, 803, 1460, 737, 885, 63, 2252, 4672, 4753, 2232, 4664, 5123, 5230, 5257, 4447, 2990, 1611, 80, 1376, 1862, 4286, 6560, 6560, 4373, 2105, 2113, 2031, 5934, 4384, 6238, 6077, 4266, 6535, 2557, 4412, 4400, 4966, 4992, 5019, 6073, 4372, 5078, 5995, 4409, 1770, 65, 68, 2004, 4432, 2508, 5776, 5046, 4426, 4593, 4515, 2472, 4211, 1862, 4130, 1943, 3724, 4671, 5644, 4448, 4393, 5149, 5409, 5176, 5934, 4635, 56, 1611, 1032, 5562, 5823, 5099, 1373, 6534, 6535, 6534, 2186, 2102, 1373, 3641, 5827, 2087, 6486, 4215, 5916, 5916, 3732, 5919, 5919, 5190, 2922, 4056, 6486, 6486, 6486, 2040, 3690, 4403, 6157, 5038, 5777, 5804, 710, 623, 2258, 4509, 4650, 5723, 6181, 4075, 4683, 5825, 5044, 5773, 4074, 4920, 2646, 2685, 5047, 5775, 6261, 2881, 5914, 731, 1505, 3935, 272, 29, 1680, 60, 4704, 259, 305, 1641, 4075, 4071, 5074, 4776, 66, 65, 1453, 573, 651, 5598, 5915, 5840, 2246, 5986, 2503, 4511, 398, 1109, 1730, 4299, 1704, 5832, 5835, 5835, 5838, 1464, 5136, 4512, 4513, 2256, 4537, 5050, 4124, 3880, 1694, 605, 2780, 5693, 5449, 6179, 1585, 2183, 4373, 6560, 4944, 5028, 2850, 5755, 6157, 5996, 6026, 6053, 1197, 2175, 434, 5108, 4420, 5937, 2013, 5652, 4564, 4379, 1802, 1801, 6537, 4356, 5024, 5348, 1955, 1957, 2112]\n", "2025-03-02 17:32:36,954 INFO llm job done, time cost: 1.405s\n", "2025-03-02 17:32:36,954 DEBUG speech_tokens: len: 266  data: [4299, 6486, 6486, 6486, 6486, 4299, 5650, 4672, 4529, 4437, 4590, 4527, 5258, 3718, 5904, 4601, 4564, 5072, 3590, 6505, 5777, 1451, 3709, 4590, 5322, 5319, 3213, 1774, 74, 1757, 1973, 5672, 5753, 3323, 1676, 1919, 1759, 1522, 4509, 4833, 2574, 5081, 5089, 1923, 2242, 4780, 4943, 2783, 6318, 4141, 1220, 680, 2900, 5587, 5587, 461, 728, 1127, 1032, 5562, 1225, 665, 5774, 6289, 4128, 6074, 2411, 32, 1100, 6537, 4347, 4214, 4871, 5609, 6259, 2058, 6378, 4676, 4686, 4534, 2989, 803, 1460, 2219, 318, 2261, 146, 4428, 4590, 4509, 2234, 4394, 5122, 4501, 4528, 2342, 3798, 80, 1214, 1697, 2186, 6560, 6560, 1457, 1355, 2140, 4218, 2031, 5937, 5850, 4456, 6077, 2594, 6534, 6507, 4727, 4427, 4967, 3453, 5668, 5749, 6070, 5098, 5051, 4379, 66, 59, 71, 1122, 4922, 2265, 5021, 6506, 4803, 4440, 4756, 4755, 1051, 4130, 4129, 4130, 1700, 809, 4509, 5482, 72, 4421, 5206, 4680, 4680, 4500, 3666, 4420, 74, 1833, 5487, 3384, 5824, 5099, 2102, 6535, 6534, 2172, 2186, 4373, 2912, 2087, 4300, 4299, 1788, 3883, 5294, 5671, 3590, 1430, 218, 4441, 4676, 4407, 3536, 6262, 6261, 4917, 2585, 5102, 5776, 4317, 4755, 3348, 2689, 5047, 5775, 6258, 6501, 2854, 5834, 11, 3665, 3935, 2, 59, 1680, 1761, 2220, 501, 359, 1639, 4315, 3832, 4857, 795, 2243, 389, 1410, 651, 2760, 5437, 5272, 4382, 2990, 6473, 74, 380, 623, 1730, 4299, 3894, 5133, 4513, 4756, 4452, 5266, 2870, 2912, 3880, 1685, 707, 3590, 6503, 3988, 1937, 2912, 2760, 5028, 2770, 5759, 6400, 6238, 3836, 1649, 1188, 2163, 1055, 4382, 5853, 3912, 6454, 4448, 5837, 740, 2043, 6540, 3591, 6401, 4871, 1957]\n", "2025-03-02 17:32:36,978 INFO yield speech index:0, len 10.00, rtf 0.151,  cost 1.512s,  all cost time 1.512s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:01<00:00,  1.51s/it]\u001b[A\u001b[A\u001b[A\u001b[A\n", "2025-03-02 17:32:37,125 INFO llm job done, time cost: 1.579s\n", "2025-03-02 17:32:37,125 DEBUG speech_tokens: len: 277  data: [2112, 3888, 3645, 5835, 5835, 5835, 3651, 1869, 4299, 2112, 4920, 4509, 4448, 5166, 5238, 5176, 5177, 3721, 3720, 4600, 5050, 2861, 4319, 4373, 1936, 4191, 5162, 4513, 4488, 2508, 5669, 5749, 5100, 2059, 3651, 5838, 5835, 3648, 1785, 4299, 5838, 5481, 4833, 127, 2894, 5095, 1032, 4484, 6481, 4214, 6318, 4140, 1304, 683, 3155, 5587, 2666, 719, 1126, 4758, 1134, 584, 3587, 5318, 3401, 140, 1118, 6537, 685, 5678, 4879, 6177, 1986, 4676, 4749, 4525, 3718, 1466, 737, 138, 315, 2252, 4428, 4590, 73, 4484, 4474, 5149, 5152, 5230, 5257, 2990, 3799, 801, 2834, 1376, 1778, 2186, 6560, 4373, 2105, 2086, 2112, 2112, 1479, 3663, 5105, 6157, 4356, 6537, 2576, 4403, 2237, 2483, 4913, 4912, 4777, 2179, 2911, 5510, 5192, 67, 65, 2258, 1842, 5405, 2265, 5021, 4803, 4803, 4675, 5242, 4650, 3481, 2753, 1943, 1943, 4130, 4130, 2986, 4996, 4914, 4439, 4393, 5205, 4753, 2493, 4492, 5853, 2225, 803, 1284, 6294, 6309, 5823, 3641, 6462, 6534, 6543, 2183, 1457, 2186, 5828, 1443, 2052, 3726, 5916, 3645, 3648, 3648, 1464, 2112, 3802, 5297, 5591, 3590, 3617, 707, 2258, 4593, 4569, 2780, 5534, 6502, 4677, 3151, 5090, 5777, 6255, 4677, 3105, 4869, 5047, 5529, 6498, 5429, 1460, 767, 1046, 29, 960, 60, 2274, 268, 62, 1638, 4315, 6020, 4966, 1590, 68, 1198, 654, 2841, 6328, 5266, 4463, 35, 3962, 3476, 56, 1028, 1972, 1704, 3645, 5835, 5835, 5835, 3648, 1788, 2112, 5136, 4513, 4759, 4606, 5266, 5051, 3395, 3884, 1676, 680, 2861, 5798, 3880, 1694, 1451, 5102, 4947, 4947, 5684, 6481, 6238, 5996, 1622, 1189, 2169, 434, 5840, 2209, 5856, 3750, 6374, 4451, 5269, 83, 1829, 4251, 6534, 2864, 3404, 1226, 1957, 4299, 4299, 4299, 1701, 3969]\n", "2025-03-02 17:32:37,158 INFO llm job done, time cost: 1.609s\n", "2025-03-02 17:32:37,158 DEBUG speech_tokens: len: 278  data: [2031, 4299, 4299, 4164, 5401, 4528, 4447, 5652, 5176, 5179, 5179, 4604, 4537, 4964, 5777, 3590, 2183, 5166, 5726, 5645, 3222, 1532, 1775, 5905, 4607, 4604, 704, 719, 2087, 2113, 5130, 4996, 2322, 5096, 5085, 57, 4537, 6157, 2756, 5589, 6327, 1223, 572, 4601, 4604, 2891, 1451, 4920, 2376, 3415, 674, 5774, 6315, 5587, 86, 767, 1747, 6534, 3485, 2693, 5446, 1905, 5650, 5161, 4533, 4450, 74, 3650, 2924, 801, 2216, 4428, 5483, 2259, 4520, 5203, 5205, 5313, 4448, 3719, 1534, 1295, 2105, 1852, 2158, 4343, 6530, 1457, 2096, 2113, 2040, 1479, 5186, 4537, 6453, 4817, 4409, 4966, 4992, 4911, 4858, 3637, 5087, 5186, 4382, 72, 56, 807, 5650, 4434, 5021, 4803, 6258, 4722, 4593, 4596, 4596, 4515, 1779, 2023, 2015, 2112, 2031, 4049, 4129, 1619, 5157, 5483, 36, 2261, 5125, 5166, 4671, 4528, 5124, 4421, 56, 1284, 5484, 4122, 5097, 2186, 6465, 6535, 6534, 2185, 2186, 2186, 4373, 2177, 2059, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 4299, 2112, 1525, 5297, 4779, 5048, 2888, 623, 2258, 5482, 4513, 2562, 5534, 6178, 1806, 4755, 5339, 5081, 5074, 5773, 5773, 4236, 6486, 6486, 4299, 4299, 4920, 4590, 3168, 5057, 5047, 6504, 4312, 5428, 1463, 857, 290, 83, 1920, 114, 4947, 620, 8, 901, 2130, 4072, 5100, 231, 2251, 1117, 2112, 651, 4872, 6167, 5998, 4463, 68, 5906, 3719, 56, 389, 299, 1756, 1947, 4299, 3651, 5238, 5486, 4678, 4579, 5508, 4946, 3386, 4123, 3872, 461, 608, 2861, 5774, 5692, 4299, 4299, 4218, 1828, 1612, 2099, 5831, 2760, 5028, 2770, 5027, 5024, 5429, 3107, 3866, 3142, 2166, 469, 5837, 4379, 5205, 6018, 1824, 5651, 4437, 4567, 3647, 749, 3909, 6534, 6534, 2899, 5672, 1946, 506, 1948, 4299, 6486, 4299]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 20 进度：1\n", "任务 20 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:37,213 INFO llm job done, time cost: 1.669s\n", "2025-03-02 17:32:37,214 DEBUG speech_tokens: len: 282  data: [2112, 3645, 5832, 5832, 5832, 3645, 3645, 1785, 5649, 4752, 4853, 5166, 4752, 5905, 6147, 5418, 4591, 5086, 4238, 2159, 1451, 2980, 5563, 5482, 5644, 5652, 5419, 803, 1535, 4428, 4753, 4671, 2909, 5088, 1200, 540, 4780, 5753, 569, 6075, 6318, 4132, 1949, 683, 2900, 5345, 5345, 4853, 725, 719, 1448, 1448, 2168, 2028, 4215, 4299, 3894, 5487, 4836, 2646, 499, 1322, 6422, 6286, 6316, 2672, 143, 764, 1801, 6534, 686, 5675, 4961, 5286, 1986, 4676, 4758, 4849, 235, 38, 3650, 1466, 147, 306, 56, 2251, 6455, 5652, 5177, 4448, 4501, 4743, 4770, 4448, 155, 3799, 71, 1295, 1771, 2101, 6560, 6559, 4373, 2186, 2149, 1948, 3159, 5832, 5832, 3645, 3645, 1542, 4299, 5853, 4376, 5509, 4266, 6507, 4844, 4517, 4913, 5641, 5101, 4615, 5827, 4835, 5192, 139, 72, 2264, 2004, 4922, 3237, 5021, 5777, 4803, 4425, 4513, 4756, 2490, 4210, 1133, 1862, 1862, 3724, 4509, 4672, 4671, 2990, 4394, 5148, 5409, 5176, 4420, 6096, 4412, 299, 1857, 5568, 4113, 5823, 4373, 4288, 6534, 6507, 2145, 2102, 1532, 5904, 4430, 6157, 4958, 3590, 692, 704, 2258, 4593, 4516, 2544, 5480, 5937, 3183, 4752, 3395, 5057, 5041, 3453, 4995, 3564, 4966, 5772, 6501, 4311, 5429, 1460, 1532, 3799, 56, 2252, 1923, 1275, 4408, 2193, 512, 89, 1639, 2128, 4311, 4832, 5020, 1605, 63, 380, 1410, 654, 5025, 5437, 5918, 89, 3722, 3798, 4439, 2567, 623, 1271, 2056, 3888, 6075, 5832, 3645, 3645, 3645, 3645, 2112, 5217, 4596, 4596, 4588, 5508, 4973, 3314, 3881, 686, 2861, 6500, 3745, 2153, 3644, 3643, 5028, 5028, 2850, 5026, 6481, 6237, 6158, 1622, 1649, 1188, 2160, 2999, 5108, 4395, 6261, 6378, 5409, 4486, 5108, 38, 3989, 6462, 6534, 2900, 6401, 1226, 1238, 2032, 4299, 4299, 4299, 4299, 4299]\n", "2025-03-02 17:32:37,214 INFO llm job done, time cost: 1.663s\n", "2025-03-02 17:32:37,214 DEBUG speech_tokens: len: 281  data: [4299, 4299, 2058, 6374, 4447, 4446, 6373, 6147, 6151, 5178, 4486, 5041, 3590, 4346, 5102, 1976, 6158, 6238, 6023, 3845, 5940, 6021, 6048, 1602, 1676, 1055, 3242, 5753, 3323, 1685, 1604, 3709, 4833, 4915, 2261, 5081, 5095, 1032, 4484, 5752, 572, 5589, 6318, 1949, 575, 5096, 4615, 3155, 1370, 1110, 5562, 1315, 1403, 5774, 6312, 6073, 224, 35, 1828, 6537, 1414, 4946, 4716, 1806, 4919, 4776, 4615, 73, 3650, 5, 57, 2990, 5157, 4753, 72, 4412, 4397, 5314, 6070, 3155, 3801, 77, 647, 1853, 2102, 5828, 6560, 725, 2056, 3888, 5832, 5916, 5916, 5835, 5832, 3645, 3645, 2031, 5853, 4375, 6157, 4275, 6534, 4520, 4643, 4912, 4992, 5073, 5020, 4042, 3643, 5051, 5189, 147, 67, 71, 3462, 4675, 3482, 5777, 5534, 4831, 4443, 4513, 4512, 4434, 1052, 2510, 2753, 3482, 3563, 4130, 1943, 1862, 3725, 5322, 4510, 63, 4415, 4393, 5206, 4671, 4446, 4492, 3663, 65, 1611, 4272, 6292, 4122, 5823, 5828, 1454, 6465, 6535, 6535, 2181, 2183, 1454, 2102, 1454, 1856, 3802, 5270, 5429, 3590, 3590, 716, 71, 4510, 4515, 376, 4967, 6179, 6262, 5415, 3069, 2909, 5048, 6502, 3471, 5562, 3564, 2780, 5777, 5776, 6501, 2845, 5837, 749, 1748, 59, 1932, 57, 2445, 539, 35, 44, 1641, 4315, 5534, 5020, 801, 137, 1362, 654, 2838, 4951, 5995, 2276, 2249, 5986, 73, 4595, 2567, 1108, 3888, 5913, 5943, 5919, 5919, 5190, 3003, 1302, 4432, 4513, 2310, 4539, 5752, 5054, 2900, 3872, 3881, 956, 623, 2777, 5045, 5774, 6256, 3772, 4124, 1454, 3644, 4947, 5028, 4947, 2843, 6401, 6158, 6157, 6238, 6023, 1679, 1679, 956, 2166, 1197, 893, 2924, 22, 5857, 1914, 6380, 3222, 4514, 4382, 47, 2045, 1804, 6456, 6534, 1413, 2837, 4871, 1238, 1951, 2112, 4299, 4299, 3975, 3732]\n", "2025-03-02 17:32:37,231 INFO llm job done, time cost: 1.677s\n", "2025-03-02 17:32:37,232 DEBUG speech_tokens: len: 281  data: [3645, 5835, 5943, 5835, 3651, 2112, 6379, 2493, 4529, 5409, 4680, 5175, 5905, 3963, 5175, 4591, 5086, 5777, 6506, 6532, 2915, 2003, 1973, 5023, 4411, 4753, 3222, 5906, 5409, 5726, 6374, 5418, 4527, 803, 1532, 5409, 4833, 2565, 2900, 5099, 2421, 540, 4727, 6400, 3407, 4860, 5589, 4135, 1304, 2894, 4612, 5342, 1127, 2742, 2646, 593, 3584, 5533, 6288, 3158, 764, 1526, 1801, 6537, 685, 6404, 4960, 3828, 4200, 4676, 4534, 2422, 2990, 1466, 2219, 1275, 65, 2261, 4510, 5726, 2502, 4664, 5122, 5391, 4527, 4519, 5986, 801, 2753, 1375, 1859, 2102, 2159, 6559, 5799, 4373, 2186, 2112, 4132, 5832, 5832, 5859, 5835, 5832, 5835, 3648, 1461, 4299, 5853, 4465, 5348, 2169, 4356, 2621, 4751, 5615, 5641, 5667, 4858, 5581, 5099, 5996, 2300, 1770, 56, 805, 2085, 4922, 2256, 5831, 6262, 5286, 4452, 4510, 4510, 4516, 312, 2023, 2005, 2032, 2112, 2032, 1942, 4130, 1538, 5166, 5644, 72, 2252, 5204, 5652, 4446, 4420, 6099, 2233, 308, 1857, 5649, 5805, 6228, 5826, 2186, 4278, 6534, 6535, 4323, 2183, 2159, 2102, 2059, 4218, 2040, 5905, 4403, 5752, 2861, 701, 1352, 2996, 4510, 4676, 2508, 6263, 6259, 3994, 4680, 2900, 5048, 5773, 3210, 4752, 3564, 5048, 6502, 6501, 5023, 5834, 803, 1775, 731, 59, 2166, 1032, 4461, 4947, 565, 305, 35, 1638, 6258, 3828, 5100, 4776, 1038, 140, 1372, 654, 2841, 5599, 5186, 8, 803, 5986, 2261, 2648, 1271, 1729, 4299, 6081, 5214, 4432, 4432, 4677, 4607, 5671, 2897, 1454, 1693, 3880, 1442, 2879, 3506, 4232, 3717, 2180, 4373, 2797, 4947, 4947, 2770, 5759, 6481, 6238, 5996, 1649, 1927, 2166, 704, 5834, 2236, 5937, 4200, 6373, 4520, 5914, 749, 1721, 1803, 4299, 2163, 5753, 3404, 1235, 1967, 4299, 6486, 6486, 6486, 6486, 6486, 4299]\n", "2025-03-02 17:32:37,280 INFO llm job done, time cost: 1.736s\n", "2025-03-02 17:32:37,281 DEBUG speech_tokens: len: 286  data: [2031, 5832, 5913, 5832, 5916, 5916, 3648, 1545, 2031, 4672, 4446, 4447, 5400, 5175, 5418, 5661, 4600, 5059, 5777, 4319, 1454, 803, 5401, 6374, 3231, 4403, 4376, 4412, 4396, 5205, 5142, 5483, 4914, 4763, 5080, 1191, 2250, 6238, 1217, 5589, 5592, 1223, 602, 2909, 5587, 5830, 2414, 719, 1457, 1774, 4998, 4077, 1403, 6506, 6259, 6289, 5345, 197, 851, 2126, 6534, 1413, 2759, 4880, 4717, 4074, 5651, 4676, 4695, 4615, 3962, 731, 1463, 139, 297, 65, 4428, 5563, 2484, 4439, 5149, 5854, 5230, 5230, 5258, 3800, 1530, 485, 2105, 1859, 2104, 6560, 5830, 2915, 716, 2028, 5346, 6075, 5832, 5913, 5832, 3645, 2112, 6015, 2206, 6238, 2513, 6534, 6534, 4843, 2252, 2237, 2456, 1259, 1258, 4938, 5019, 6310, 3616, 5087, 5185, 4460, 63, 68, 804, 4191, 5408, 4696, 5047, 6424, 4803, 4434, 5405, 4433, 4678, 3480, 3968, 1052, 2024, 2185, 3886, 1862, 2986, 5320, 5401, 2261, 4421, 5854, 4672, 2241, 4439, 5934, 2232, 4439, 802, 2058, 5568, 4122, 5824, 6560, 4288, 6535, 6291, 6507, 2101, 2102, 1457, 5099, 1947, 5913, 5832, 5832, 3648, 1626, 1659, 3875, 5509, 2861, 2132, 719, 797, 4512, 4435, 4677, 5020, 5533, 6260, 1266, 4755, 5582, 5075, 5777, 5772, 2076, 4836, 4077, 2769, 5047, 6504, 4314, 5428, 731, 1532, 1532, 32, 1923, 2301, 2787, 638, 116, 2124, 4315, 3832, 5073, 2373, 69, 137, 1443, 654, 2838, 5598, 5267, 2276, 68, 5905, 3961, 2324, 704, 1352, 1732, 5832, 5832, 5835, 5835, 3648, 3732, 4137, 4513, 4756, 4758, 4570, 5753, 2870, 1442, 4124, 1937, 716, 3590, 6506, 5798, 4097, 2153, 5828, 2913, 2841, 5028, 5074, 6484, 6481, 6023, 6050, 6050, 2166, 2160, 5189, 4378, 4392, 6180, 4281, 5644, 4519, 5108, 1073, 2045, 4269, 6535, 712, 6401, 3413, 506, 1951, 3645, 5832, 5832, 3645, 3645]\n", "2025-03-02 17:32:37,334 INFO llm job done, time cost: 1.785s\n", "2025-03-02 17:32:37,335 DEBUG speech_tokens: len: 288  data: [2032, 4299, 6486, 6486, 6486, 4299, 2112, 4675, 2340, 4528, 4509, 5175, 5907, 5179, 4594, 5086, 6532, 4346, 4370, 2980, 4590, 4594, 4752, 1533, 1535, 3152, 5827, 6560, 2186, 2087, 1522, 5319, 5319, 154, 2894, 5089, 387, 4681, 5510, 2675, 6318, 4142, 575, 713, 5507, 5507, 713, 641, 1842, 4998, 3375, 502, 1403, 4235, 6262, 5560, 2420, 113, 1526, 2070, 6540, 4320, 4133, 4955, 4879, 6261, 2004, 5648, 4431, 5020, 2347, 3962, 1463, 3653, 138, 63, 59, 4509, 4591, 4428, 2252, 4496, 5935, 5962, 5337, 5333, 1612, 1615, 1376, 1366, 1771, 2093, 4372, 5829, 5830, 2186, 2059, 4218, 2112, 3888, 5832, 5832, 5835, 5835, 5835, 5838, 5835, 1785, 2121, 5122, 4456, 2169, 3519, 4592, 4750, 4993, 5074, 4858, 5096, 4808, 5192, 112, 63, 2249, 879, 5650, 78, 5102, 5532, 4803, 4444, 4513, 4516, 285, 566, 1285, 2104, 4127, 1862, 5974, 4593, 2325, 2336, 4504, 5125, 4509, 2331, 4486, 3912, 4393, 38, 1041, 6294, 4122, 5828, 728, 1454, 6534, 6535, 6456, 2159, 4373, 1457, 2086, 1760, 1606, 6071, 5999, 5671, 2861, 665, 848, 4509, 4432, 366, 5804, 6262, 2994, 6060, 3629, 5057, 5048, 6502, 3993, 5565, 3861, 2685, 5039, 5047, 6504, 6498, 5915, 1463, 1505, 1747, 29, 867, 2220, 4947, 511, 359, 8, 1644, 2128, 3104, 5073, 795, 2255, 380, 654, 651, 5355, 5996, 3731, 818, 1616, 6067, 47, 4844, 1280, 2000, 4299, 6486, 3975, 4218, 4137, 4407, 4756, 4515, 4540, 5026, 2906, 4124, 1613, 1199, 707, 692, 4319, 6425, 1987, 4299, 4218, 2074, 4016, 4124, 728, 3644, 3643, 5028, 5028, 2877, 5678, 6481, 5509, 5294, 1652, 1919, 2172, 2160, 2999, 5111, 2201, 5856, 1797, 5648, 2259, 4463, 2192, 1828, 2046, 4299, 2160, 3485, 1220, 1949, 4299, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6405]\n", "2025-03-02 17:32:37,353 INFO llm job done, time cost: 1.804s\n", "2025-03-02 17:32:37,354 DEBUG speech_tokens: len: 289  data: [3888, 5832, 5835, 5835, 3651, 1788, 3462, 4429, 4528, 4680, 4914, 4447, 5904, 5907, 5260, 4537, 4964, 6506, 4346, 4373, 4370, 2990, 4833, 4752, 4671, 1531, 5177, 4671, 4753, 4671, 5175, 5420, 5167, 4833, 2565, 713, 5099, 1191, 64, 5753, 3485, 4860, 5592, 3415, 572, 704, 4859, 4858, 2657, 725, 1125, 5649, 5562, 3483, 1403, 4316, 6047, 6315, 5342, 221, 29, 1316, 2046, 6534, 4356, 5672, 4883, 5040, 1887, 5650, 4443, 4777, 2344, 5987, 2, 1463, 62, 885, 315, 2243, 2990, 4752, 4752, 2484, 4529, 4501, 5229, 4500, 4447, 3719, 3799, 79, 1295, 1376, 1697, 2183, 6560, 5830, 5804, 1430, 2140, 4132, 3645, 5835, 5838, 3732, 4299, 1482, 5850, 5834, 3242, 4347, 6534, 2639, 2327, 2699, 5615, 5641, 5667, 5020, 3884, 4373, 5099, 4538, 5270, 2249, 876, 1269, 68, 798, 1977, 4676, 2238, 4994, 5776, 4803, 4431, 4675, 4675, 4675, 2499, 4211, 323, 1763, 3648, 5838, 5838, 3651, 1788, 1906, 4130, 1538, 4590, 4672, 2259, 4519, 4501, 4428, 4680, 4500, 5856, 5121, 4439, 804, 4191, 6291, 6310, 5825, 3641, 6534, 6510, 4362, 1457, 1451, 3881, 3887, 6026, 6481, 2861, 4319, 692, 875, 4512, 4515, 2454, 4723, 6262, 3228, 3153, 5087, 4963, 1887, 4677, 6291, 2754, 5065, 5043, 6258, 4312, 3647, 776, 3962, 2192, 150, 57, 2517, 501, 557, 62, 1557, 4071, 3829, 5073, 4533, 297, 146, 1383, 651, 2835, 5356, 5267, 8, 80, 5906, 2990, 56, 551, 2003, 1948, 3889, 5832, 5835, 5832, 3648, 1626, 2949, 4513, 4679, 4507, 5428, 5057, 722, 4046, 4124, 461, 2888, 5774, 3746, 1749, 4124, 1454, 5831, 4983, 5028, 4947, 5038, 5509, 5509, 5429, 3161, 488, 2756, 1197, 2163, 434, 2921, 2201, 5937, 2067, 6377, 4447, 4486, 2189, 1397, 1801, 6510, 6537, 712, 5753, 2684, 503, 2041, 4299, 4215, 4215, 4056, 3972, 4299, 4299]\n", "2025-03-02 17:32:37,372 INFO llm job done, time cost: 1.828s\n", "2025-03-02 17:32:37,373 DEBUG speech_tokens: len: 291  data: [5832, 5835, 5943, 5832, 1458, 1896, 4672, 4419, 4519, 5166, 5645, 3231, 6148, 6390, 4447, 4807, 5048, 5804, 5830, 5093, 1271, 1973, 6238, 5995, 6023, 1649, 1676, 1613, 4590, 4834, 135, 5078, 5013, 1113, 2242, 5671, 4943, 5589, 6318, 1952, 602, 5087, 4858, 4858, 623, 1454, 2742, 3375, 1308, 2051, 6422, 6316, 5345, 86, 848, 1801, 6534, 581, 4955, 4717, 4074, 3462, 4675, 4776, 2425, 803, 3653, 59, 1272, 2261, 5167, 4754, 2484, 4484, 4397, 5207, 5314, 5332, 3800, 3798, 77, 566, 1295, 1700, 2183, 4372, 5829, 5829, 728, 1435, 5346, 5835, 5946, 5946, 5838, 3651, 1464, 4299, 3747, 5121, 4537, 2441, 6534, 3591, 4592, 4418, 4643, 4912, 3454, 5641, 5019, 5020, 5345, 3644, 2915, 5024, 5108, 2195, 66, 74, 76, 2013, 4433, 2508, 5075, 6182, 4803, 4443, 4673, 4678, 4678, 3480, 4211, 5669, 4292, 4130, 1943, 1538, 4431, 4754, 2493, 4442, 4393, 5125, 4996, 2232, 2286, 3666, 4439, 73, 2085, 5001, 3384, 5824, 3644, 3644, 6507, 6507, 6483, 1457, 1457, 1457, 725, 2909, 1838, 1759, 1519, 3875, 5293, 4943, 2696, 3590, 1430, 725, 137, 4431, 4516, 4650, 5020, 5938, 6181, 4683, 2666, 2879, 5693, 5449, 5529, 2139, 4920, 4833, 3564, 5048, 5692, 5769, 2854, 5837, 5, 3962, 1046, 2219, 1680, 60, 4461, 2679, 314, 35, 1561, 4315, 6020, 4992, 2347, 1044, 2324, 1443, 654, 651, 2685, 5437, 5189, 35, 77, 6229, 2261, 4529, 641, 1351, 3645, 5835, 5919, 5946, 5835, 5838, 816, 2949, 4513, 4759, 4776, 4536, 5054, 2894, 4124, 4124, 1199, 692, 5693, 5773, 2012, 1984, 4124, 4367, 4373, 2841, 2841, 5028, 2770, 5755, 5752, 6239, 3836, 1676, 1928, 2175, 1431, 5105, 4406, 5854, 1887, 5405, 4437, 4540, 2198, 1721, 1805, 4275, 6534, 3627, 4780, 4862, 506, 1238, 1948, 2112, 4299, 4299, 4299, 6486, 4299, 4299, 6486, 6486, 4299]\n", "2025-03-02 17:32:37,379 INFO yield speech index:0, len 10.44, rtf 0.247,  cost 2.579s,  all cost time 2.579s\n", "100%|██████████| 1/1 [00:02<00:00,  2.58s/it]\n", "2025-03-02 17:32:37,404 INFO llm job done, time cost: 1.854s\n", "2025-03-02 17:32:37,405 DEBUG speech_tokens: len: 292  data: [3645, 5835, 5946, 5919, 5190, 1869, 5650, 4437, 4600, 5410, 5726, 5418, 3719, 6390, 5418, 4567, 4816, 3590, 5777, 5831, 3644, 2990, 4833, 5644, 6376, 1773, 803, 2166, 2163, 3566, 6077, 5834, 5105, 2225, 5725, 2484, 5015, 5094, 2085, 4727, 5509, 2759, 4860, 5589, 1229, 575, 683, 5582, 5344, 2909, 641, 2733, 4833, 1308, 1403, 5528, 6315, 5345, 140, 767, 2070, 6537, 3485, 4865, 4880, 6012, 1275, 5648, 4678, 4614, 3069, 29, 1466, 59, 1044, 2252, 2261, 6455, 6374, 4420, 4421, 5365, 5202, 5148, 5176, 2990, 3800, 1504, 404, 2105, 1861, 2012, 2186, 5830, 6559, 1454, 2080, 3645, 5190, 5916, 5838, 2922, 2112, 3663, 4379, 5185, 4356, 6537, 4844, 2330, 539, 4912, 5694, 4911, 4938, 5587, 3638, 4372, 5090, 5267, 5189, 2303, 157, 1278, 68, 885, 4191, 4676, 2508, 4913, 5534, 6259, 5533, 4431, 4513, 4675, 2499, 3968, 1295, 2024, 2105, 1943, 3725, 4428, 5726, 4671, 4529, 5125, 5206, 4672, 2232, 4439, 5853, 5937, 4447, 299, 1126, 2085, 5001, 5562, 6309, 5823, 6560, 4373, 4373, 6534, 6507, 6537, 6534, 2185, 1457, 2186, 2093, 1760, 1525, 5986, 5996, 4955, 674, 701, 677, 71, 4510, 4516, 2563, 5453, 6262, 4686, 3393, 2903, 5777, 5529, 1833, 4836, 2835, 5038, 5043, 6501, 5041, 5834, 749, 3908, 281, 56, 1932, 786, 5028, 510, 278, 35, 1560, 4312, 5534, 4992, 159, 72, 137, 2181, 651, 2835, 5598, 5266, 4544, 140, 3718, 5985, 4448, 2585, 551, 1729, 3645, 5916, 5838, 5838, 5109, 1545, 5136, 5239, 4434, 4567, 4943, 2894, 3881, 1685, 623, 728, 5777, 5771, 6040, 1856, 2102, 5830, 2760, 5028, 2769, 5756, 5509, 5266, 5270, 194, 950, 1207, 2148, 2163, 2594, 4379, 2938, 5938, 4191, 5652, 4511, 4378, 128, 4313, 4275, 6535, 1413, 5753, 5591, 509, 1967, 2032, 4299, 4299, 4299, 6486, 4299, 4299, 4299, 4299, 4299, 4299]\n", "2025-03-02 17:32:37,423 INFO llm job done, time cost: 1.880s\n", "2025-03-02 17:32:37,423 DEBUG speech_tokens: len: 295  data: [2028, 5835, 5835, 5835, 5835, 2922, 2112, 5651, 2223, 4448, 4672, 4671, 5905, 5908, 5421, 5178, 4567, 4988, 5696, 5804, 5830, 5827, 3638, 3881, 3718, 62, 2834, 3509, 6259, 6258, 4299, 4299, 2112, 2112, 1815, 4299, 4299, 4299, 4299, 4299, 4299, 2031, 2112, 3192, 4836, 4833, 2565, 2657, 5098, 2583, 297, 4780, 6400, 5672, 6075, 6075, 6318, 1220, 1952, 2867, 4609, 5586, 5342, 722, 722, 2490, 3348, 509, 2861, 5690, 6316, 3401, 116, 119, 1828, 6534, 686, 4862, 4883, 5203, 3993, 6378, 4919, 5424, 4534, 5986, 2, 3650, 2219, 309, 65, 2264, 5482, 6377, 2250, 4442, 4420, 5205, 5151, 5175, 4448, 5906, 1531, 395, 1295, 1376, 1859, 4372, 6532, 6559, 6560, 725, 2055, 3645, 3648, 3648, 1461, 2112, 1563, 5853, 4490, 4850, 3763, 6534, 4356, 4844, 4484, 47, 26, 2699, 4965, 4992, 4858, 5096, 3643, 5024, 5917, 2222, 801, 29, 77, 2085, 4676, 2265, 5101, 6262, 4800, 4443, 4676, 4433, 2229, 3239, 2105, 1943, 1943, 1295, 4431, 4753, 2253, 4451, 5206, 4440, 4437, 4439, 5853, 4421, 74, 1833, 5568, 4122, 5580, 5099, 1373, 6534, 6535, 4359, 2186, 1457, 725, 3395, 3802, 5988, 6002, 5995, 4883, 3590, 1403, 1457, 707, 68, 4509, 4432, 4677, 4994, 6262, 6262, 4512, 3151, 5054, 5773, 6502, 5484, 1134, 2689, 5047, 5043, 6501, 2855, 5834, 11, 3692, 38, 29, 1932, 789, 4461, 2679, 557, 35, 130, 4071, 6259, 6015, 5073, 5100, 885, 2246, 56, 1443, 654, 2841, 4947, 5680, 5996, 2195, 62, 5906, 6229, 2252, 380, 623, 1324, 3645, 5835, 3648, 1788, 5109, 4512, 4513, 2499, 4544, 5671, 2813, 4124, 3884, 704, 2861, 5447, 3670, 1616, 1454, 5831, 2877, 5028, 4866, 6401, 6239, 5267, 3863, 3863, 1197, 2166, 712, 5189, 4406, 5853, 5937, 2085, 6374, 4447, 4536, 4378, 47, 1802, 4275, 6534, 4356, 5051, 6320, 1226, 1964, 1957, 4299, 4299, 4299]\n", "2025-03-02 17:32:37,522 INFO llm job done, time cost: 1.972s\n", "2025-03-02 17:32:37,523 DEBUG speech_tokens: len: 299  data: [4299, 4299, 4299, 1734, 5644, 2260, 2252, 5645, 5652, 3961, 3719, 3719, 5905, 5418, 4592, 2861, 4319, 5804, 1457, 2980, 4996, 5401, 1774, 6148, 2751, 3147, 968, 707, 1358, 2031, 5346, 5859, 5859, 5859, 5859, 1542, 5376, 5482, 2556, 2898, 63, 4780, 5429, 6084, 6075, 1220, 656, 4853, 4616, 2891, 719, 387, 4752, 2754, 1403, 3587, 6046, 6317, 224, 29, 1883, 6546, 6534, 2762, 4880, 5203, 1806, 6380, 4443, 4533, 5987, 2921, 3653, 139, 72, 110, 4428, 5726, 2233, 4664, 5365, 5391, 4448, 3799, 80, 1376, 1769, 2186, 6559, 5101, 728, 2086, 3402, 3672, 5859, 5859, 5862, 5859, 3645, 3645, 3645, 1461, 4299, 1806, 3666, 4406, 4841, 4859, 3789, 6534, 6537, 3627, 4592, 2225, 776, 1511, 1745, 2006, 2006, 5020, 5019, 5020, 3803, 2186, 4372, 5098, 4537, 5192, 2249, 1842, 65, 59, 1275, 5405, 79, 5021, 5777, 5529, 5235, 5161, 5486, 5405, 4676, 1050, 1295, 1286, 2112, 4299, 4299, 4299, 1978, 4130, 6317, 4049, 1781, 4431, 6374, 5401, 4448, 5125, 5286, 5418, 6458, 3222, 4446, 6018, 2205, 56, 1773, 3543, 5565, 4285, 5826, 5831, 4288, 6534, 6538, 6537, 2186, 1376, 1373, 2003, 1978, 1774, 6148, 5186, 2687, 3590, 1403, 623, 2255, 5726, 4435, 2807, 3995, 6505, 2499, 3060, 2900, 5777, 5773, 3471, 4833, 2754, 5038, 5047, 6501, 5770, 5915, 740, 3665, 245, 5, 1923, 2247, 4704, 2760, 647, 305, 35, 1887, 4074, 3832, 5100, 4777, 66, 116, 1360, 654, 2838, 5355, 5999, 2195, 2264, 6229, 2260, 4844, 542, 1732, 1701, 5832, 5859, 5859, 5862, 3675, 1545, 3192, 5404, 5405, 2337, 4537, 5029, 2816, 3881, 803, 656, 5777, 5771, 3852, 1045, 2186, 3644, 2914, 5025, 5757, 5101, 6484, 6401, 6481, 6239, 1622, 1676, 1926, 4299, 1431, 3728, 4382, 5125, 6262, 1824, 6379, 4437, 5185, 5108, 1883, 2128, 6456, 6535, 1440, 6401, 2693, 1967, 2041, 2112, 4299, 4299, 4299, 5919, 3651]\n", "2025-03-02 17:32:37,541 INFO llm job done, time cost: 1.996s\n", "2025-03-02 17:32:37,542 DEBUG speech_tokens: len: 301  data: [2028, 3888, 5835, 5832, 5859, 3915, 2058, 6377, 2502, 4529, 4428, 4753, 4680, 6149, 3962, 3960, 5175, 4591, 5050, 2861, 6506, 6560, 5831, 5099, 2915, 1457, 2105, 2006, 2096, 6479, 4292, 2105, 2186, 2105, 2006, 2032, 4299, 4137, 4755, 4753, 2493, 2900, 5070, 390, 4681, 6481, 6401, 3412, 6318, 4144, 1304, 2897, 5095, 4616, 155, 1373, 1047, 4752, 3411, 674, 5690, 5587, 5588, 116, 1010, 2044, 6537, 685, 5672, 4879, 6015, 2067, 5651, 4695, 4776, 2992, 2990, 3650, 737, 67, 2261, 2243, 4428, 4753, 2493, 4520, 4421, 5203, 4501, 5258, 2990, 3799, 77, 647, 1853, 2011, 2105, 4373, 5831, 1457, 2177, 2113, 6486, 3975, 5835, 5835, 5838, 5109, 5109, 2112, 1722, 5931, 4456, 6238, 6401, 3511, 6534, 3627, 4565, 2222, 2456, 1259, 1987, 5019, 5343, 4369, 3643, 5051, 5996, 5921, 2249, 801, 2243, 380, 65, 1923, 4922, 2337, 5101, 5533, 5532, 4407, 4593, 4488, 2508, 3482, 1043, 1277, 2059, 2006, 6317, 4130, 1943, 1052, 5173, 4590, 4752, 2259, 4520, 4393, 5125, 4672, 2484, 4448, 4393, 6093, 56, 1046, 1284, 5562, 6553, 5098, 4373, 6465, 6507, 2145, 2186, 2186, 2090, 1609, 6062, 5995, 4955, 5777, 3617, 710, 866, 4407, 4593, 2301, 4994, 5938, 3912, 4752, 5096, 5066, 5772, 2130, 4920, 5319, 3564, 5044, 5776, 5046, 6501, 2854, 5918, 1460, 775, 3719, 3233, 56, 272, 785, 1812, 5832, 5835, 3648, 4299, 4299, 762, 4380, 2760, 512, 17, 17, 832, 6261, 3828, 5074, 4913, 160, 63, 137, 1443, 651, 2838, 5680, 5995, 3734, 278, 1535, 5987, 2504, 2567, 2000, 1975, 3645, 5835, 5835, 3732, 4299, 5136, 4510, 4516, 2562, 5266, 5675, 719, 3638, 4124, 1685, 596, 674, 6425, 1988, 2068, 2071, 1855, 2180, 3644, 3644, 4947, 5028, 5028, 2852, 6485, 5752, 6265, 3863, 1679, 955, 2166, 704, 3650, 4388, 5856, 1887, 6379, 2259, 4459, 2192, 830, 3991, 4251, 6534, 2845, 6158, 1226, 1961, 1948]\n", "2025-03-02 17:32:37,556 INFO llm job done, time cost: 2.007s\n", "2025-03-02 17:32:37,557 DEBUG speech_tokens: len: 301  data: [2112, 3645, 5919, 5838, 5838, 5835, 1461, 4299, 5649, 5400, 4528, 6381, 5148, 6148, 6384, 5178, 4483, 4961, 5776, 5074, 5831, 1451, 4671, 6374, 4194, 1532, 5409, 5648, 4406, 5108, 4394, 5122, 6455, 2493, 4754, 5098, 2583, 540, 4699, 5672, 572, 6318, 6318, 4132, 1304, 683, 1451, 5587, 3158, 707, 1370, 4929, 3375, 579, 674, 6422, 6259, 6316, 221, 788, 1586, 4353, 4347, 2759, 4880, 5689, 6015, 4191, 4676, 4407, 4525, 3799, 737, 737, 147, 316, 29, 2251, 6535, 6373, 2504, 4961, 5447, 5959, 5330, 2333, 1611, 404, 1862, 2011, 4373, 6559, 3644, 1355, 1944, 3645, 5835, 5835, 3648, 3648, 1458, 4299, 6486, 4299, 1479, 3666, 4376, 6157, 6077, 2513, 6534, 6537, 6534, 2657, 4592, 4430, 748, 3667, 1501, 1978, 3481, 5019, 5344, 1452, 2914, 5509, 3005, 151, 63, 2249, 798, 6380, 4686, 5669, 5776, 5532, 4440, 5486, 4676, 3237, 1052, 1286, 2005, 2096, 1943, 1943, 1538, 4671, 6458, 5643, 4448, 5122, 6096, 6457, 2502, 4420, 5937, 2259, 56, 1125, 3516, 5565, 6229, 5825, 3641, 6534, 6535, 6507, 2185, 2186, 728, 728, 2903, 4299, 3888, 3969, 3648, 3729, 3648, 3645, 1782, 4299, 4299, 4299, 1950, 1506, 3718, 4456, 4942, 2780, 3590, 4373, 710, 704, 2984, 4753, 4434, 2591, 5776, 6262, 4929, 2340, 5081, 5776, 5770, 5658, 5562, 3492, 5039, 5044, 5772, 6501, 5033, 5105, 734, 1504, 3668, 3663, 2, 353, 299, 299, 299, 1271, 1757, 2139, 60, 2517, 511, 143, 1638, 4314, 5534, 4776, 1029, 32, 713, 573, 651, 2763, 5924, 5999, 2195, 3718, 5986, 2243, 380, 623, 1972, 1704, 5913, 5919, 5919, 5838, 1869, 2031, 5161, 6374, 4677, 4570, 6481, 5057, 4124, 3881, 1685, 605, 1322, 3590, 5798, 6203, 3960, 4042, 1370, 3644, 5830, 2841, 5028, 4954, 6481, 6481, 3809, 1652, 1198, 2166, 461, 5108, 2206, 5856, 1563, 6380, 4448, 5111, 41, 1559, 4332, 6540, 1440, 5672, 3413, 1235, 1951]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 1 进度：1\n", "任务 1 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:37,607 INFO llm job done, time cost: 2.062s\n", "2025-03-02 17:32:37,608 DEBUG speech_tokens: len: 305  data: [4218, 6486, 6486, 6486, 3894, 5835, 5835, 5835, 5838, 3651, 4299, 1950, 4918, 2261, 4437, 5725, 4447, 4447, 5419, 5176, 4483, 5032, 6425, 6532, 2912, 3799, 5988, 2263, 4596, 4835, 680, 1439, 2140, 4299, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 4299, 3651, 4725, 5645, 2484, 2909, 5098, 297, 4807, 4943, 5346, 6075, 3898, 1220, 710, 5825, 5587, 4615, 5087, 725, 1041, 5322, 1234, 3587, 5530, 6261, 6072, 224, 35, 1802, 4269, 4347, 3407, 4880, 5286, 1806, 4676, 4488, 4749, 4525, 2990, 3650, 8, 147, 64, 2252, 4752, 4996, 2259, 4403, 5122, 5151, 4419, 4519, 5906, 802, 386, 566, 1853, 2068, 4264, 6559, 5830, 1457, 1358, 2087, 2032, 4299, 3888, 5832, 5832, 5832, 5835, 5835, 3729, 2040, 3744, 4376, 4457, 6534, 3600, 2300, 4886, 4967, 5046, 5073, 4535, 5830, 5093, 5509, 5188, 63, 2243, 879, 5649, 4432, 4697, 5074, 5775, 4830, 4512, 4513, 4596, 1050, 1295, 2105, 1942, 1943, 3077, 4509, 4753, 2260, 4439, 5202, 4671, 4752, 4500, 2934, 4412, 65, 2094, 6459, 6294, 5580, 5094, 3563, 1376, 6426, 6535, 6456, 2185, 1457, 2186, 3641, 2000, 1759, 3883, 6026, 5023, 6506, 692, 470, 4431, 4513, 4488, 2482, 6262, 6261, 1806, 4755, 3393, 2894, 5773, 6501, 4677, 6291, 2775, 5070, 5529, 6255, 2612, 3650, 749, 3691, 2, 138, 1761, 2193, 4947, 591, 305, 850, 1887, 4074, 3828, 5100, 4768, 792, 2246, 1848, 654, 2841, 5598, 5266, 5111, 8, 806, 5909, 5905, 2243, 2567, 299, 1757, 1947, 5832, 5832, 5859, 5862, 3651, 4299, 5379, 4513, 4512, 4758, 4614, 5509, 4946, 2915, 1454, 4367, 4042, 1199, 599, 674, 4319, 4319, 5797, 3961, 6219, 1937, 2102, 1457, 5831, 2760, 2838, 2841, 2859, 5023, 5509, 6238, 6238, 3809, 1652, 1676, 1449, 2172, 2621, 5837, 2198, 5937, 1824, 5648, 4447, 4462, 3650, 20, 3908, 4242, 6537, 3627, 5753, 6320, 500, 1958, 4215, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 4218]\n", "2025-03-02 17:32:37,624 INFO llm job done, time cost: 2.073s\n", "2025-03-02 17:32:37,624 DEBUG speech_tokens: len: 305  data: [3888, 5832, 5835, 5862, 5835, 3648, 2112, 3462, 4672, 2261, 5176, 5166, 5905, 5906, 5421, 5175, 4511, 4979, 5804, 6532, 5831, 1250, 1979, 5671, 4780, 2225, 5401, 5648, 3225, 1781, 4211, 5750, 5749, 5182, 4753, 4752, 2549, 5093, 2907, 306, 4538, 5024, 569, 3403, 6318, 1946, 1301, 707, 4853, 5587, 5345, 2657, 1457, 1854, 5001, 3348, 590, 674, 6179, 4128, 6317, 35, 788, 1846, 6459, 4347, 4217, 4961, 4960, 3828, 5650, 4676, 4533, 2335, 2990, 3650, 2222, 1119, 56, 794, 4428, 4833, 4752, 2333, 4493, 5206, 5205, 5230, 5986, 5987, 1611, 80, 2024, 2024, 2059, 2068, 6559, 5830, 1457, 2186, 2109, 5913, 5838, 5835, 5835, 2922, 2112, 4299, 3663, 4457, 5914, 2270, 6453, 6534, 4835, 4433, 2240, 4966, 4992, 4992, 5587, 3640, 5078, 5998, 2299, 315, 2246, 1851, 4921, 2229, 2834, 5775, 4831, 4429, 5405, 5405, 321, 1295, 1376, 1835, 4022, 1538, 4509, 4672, 2250, 4430, 5126, 5421, 4671, 4412, 4393, 3669, 2234, 1773, 3462, 6294, 6525, 5097, 5831, 2186, 6534, 6534, 6537, 2186, 1457, 1454, 1454, 1454, 2003, 1763, 3724, 5324, 6401, 2861, 4319, 716, 797, 4510, 4677, 5021, 6181, 3995, 4683, 3638, 5777, 5773, 1833, 5565, 2754, 5047, 5047, 6501, 2854, 6157, 1463, 1613, 1046, 2, 1932, 1842, 2193, 4947, 539, 8, 44, 1803, 6501, 4068, 5073, 4992, 160, 1758, 2327, 380, 596, 623, 1271, 1973, 4299, 6486, 6486, 1302, 654, 5028, 5680, 5995, 5192, 278, 889, 5906, 6066, 4438, 4771, 2504, 623, 1271, 1947, 3645, 5835, 5838, 5838, 5838, 5838, 5838, 5109, 1464, 4137, 4429, 4754, 4678, 4606, 5509, 4945, 5084, 713, 1697, 1697, 713, 710, 1403, 6506, 6506, 5798, 3776, 3708, 1613, 2183, 5831, 2913, 4299, 2841, 4947, 5599, 6481, 5509, 5995, 1649, 1676, 3863, 2160, 2166, 2837, 5834, 5108, 2209, 5856, 1806, 6380, 5409, 4540, 3650, 1559, 2073, 6535, 6534, 2863, 6481, 2693, 1967, 1984, 4299, 4299, 4299, 4299, 4218]\n", "2025-03-02 17:32:37,675 INFO llm job done, time cost: 2.129s\n", "2025-03-02 17:32:37,675 DEBUG speech_tokens: len: 309  data: [2031, 3159, 5859, 5862, 5862, 5832, 3648, 2112, 3462, 4680, 4520, 4437, 4753, 5176, 5176, 3721, 5421, 4448, 4483, 4988, 3590, 4319, 2159, 2186, 2175, 4299, 4347, 3566, 6077, 6086, 5834, 5915, 4465, 2028, 1947, 3645, 5832, 5835, 5859, 5859, 3645, 4299, 5352, 4833, 4752, 4752, 2333, 2912, 5094, 2085, 2251, 5509, 3485, 2759, 6318, 4142, 1304, 683, 3155, 5588, 479, 641, 2490, 5562, 3483, 2861, 3506, 6047, 6316, 3158, 116, 38, 1881, 6534, 659, 5675, 4880, 6012, 1986, 4919, 4675, 4938, 4606, 5986, 734, 1463, 62, 1770, 65, 2252, 4428, 4509, 2250, 4439, 4502, 5152, 5233, 4528, 5905, 1614, 80, 1457, 1862, 2099, 4373, 6556, 6560, 4373, 1457, 2168, 2032, 4299, 6486, 6486, 4299, 3663, 4376, 5914, 433, 6453, 6426, 4511, 2255, 2240, 4885, 4911, 5100, 5581, 3643, 5051, 5918, 2249, 1044, 2258, 1131, 4921, 2256, 5021, 5776, 4803, 4431, 4513, 4675, 2508, 1376, 1376, 2105, 4049, 3887, 1943, 3725, 4509, 4510, 2241, 4433, 4423, 4476, 4440, 4428, 4528, 5850, 4420, 2261, 1531, 2004, 5565, 4113, 5581, 5099, 3644, 6534, 6534, 6534, 2185, 2177, 1364, 1526, 3802, 5996, 5756, 674, 2132, 716, 2336, 4512, 4516, 2482, 5452, 6178, 3994, 5238, 2657, 5063, 5048, 5773, 6177, 2139, 5001, 5562, 5670, 4954, 5048, 6425, 6501, 6501, 3583, 6158, 3647, 11, 3668, 3961, 299, 56, 2166, 2085, 2217, 4947, 2724, 305, 35, 1587, 4074, 6258, 4805, 5046, 4939, 1932, 297, 59, 299, 2009, 2139, 654, 3567, 5355, 5266, 4382, 2990, 6148, 56, 140, 4444, 4593, 2328, 4541, 4946, 683, 1937, 3880, 1433, 710, 1457, 2177, 4299, 1947, 5346, 6075, 6075, 5832, 5832, 3969, 3969, 2109, 1908, 1694, 2180, 3644, 5100, 4947, 5028, 5028, 5065, 6485, 6320, 6158, 6238, 6023, 6050, 3863, 3872, 1449, 2175, 461, 5834, 2918, 2207, 5857, 6262, 4191, 4672, 4415, 5266, 5108, 749, 3992, 4275, 6537, 3627, 5753, 6320, 509, 1958, 1948, 4299, 6486, 6486, 6486, 6486, 6405]\n", "2025-03-02 17:32:37,826 INFO llm job done, time cost: 2.273s\n", "2025-03-02 17:32:37,826 DEBUG speech_tokens: len: 317  data: [1950, 3645, 5835, 5916, 5862, 5916, 5916, 5835, 1545, 2112, 6380, 4672, 4528, 5175, 5652, 5418, 5418, 4528, 4484, 5045, 5777, 4346, 6533, 5830, 1439, 1976, 1976, 6238, 6238, 3809, 3839, 5899, 4590, 4671, 1774, 5167, 4996, 5645, 315, 5108, 5837, 4388, 5122, 5121, 1501, 2112, 5835, 5916, 5916, 1788, 2112, 5376, 4833, 2403, 5069, 4851, 300, 4564, 5671, 3403, 5589, 1958, 575, 710, 5582, 5829, 5345, 2891, 725, 1373, 1839, 5001, 3375, 1224, 1322, 4319, 5446, 6315, 6315, 2420, 89, 764, 1829, 4278, 6507, 2684, 4862, 5611, 5772, 3993, 5651, 4686, 4525, 3719, 734, 3653, 148, 309, 74, 4428, 4996, 2241, 2243, 4424, 5125, 4503, 5179, 2264, 3718, 1611, 80, 647, 1769, 2093, 4372, 6533, 6560, 3644, 725, 1448, 2059, 4131, 3888, 5832, 3645, 3645, 3648, 1788, 4299, 1887, 4393, 6157, 5428, 6453, 6534, 4763, 4427, 4966, 4992, 5667, 5020, 5342, 3643, 5090, 5510, 5270, 2222, 879, 2252, 2243, 1617, 6378, 4676, 2238, 5074, 6180, 4803, 4434, 4756, 4675, 4650, 3238, 2753, 1295, 1295, 2105, 4130, 4130, 4130, 1700, 1538, 4752, 4915, 72, 2255, 4396, 5205, 5645, 2223, 4411, 6099, 4392, 2261, 2022, 5730, 6291, 6552, 5831, 3560, 6507, 6535, 6537, 4359, 2105, 1457, 1451, 3799, 3884, 5999, 5429, 2861, 2132, 1430, 1352, 4408, 4593, 2463, 4940, 5856, 6262, 5484, 4122, 5081, 5777, 5774, 4314, 5001, 3375, 4863, 5048, 6504, 6501, 3583, 5914, 11, 1019, 1775, 2, 2246, 1923, 30, 2517, 512, 278, 44, 1563, 4314, 5534, 4992, 2266, 297, 32, 623, 1383, 651, 2757, 6085, 5915, 737, 71, 3962, 6392, 2252, 2567, 1028, 1732, 1947, 6075, 5832, 5916, 5916, 5916, 5190, 1869, 2949, 4512, 4515, 4552, 5752, 3629, 4124, 1199, 2882, 5774, 6283, 6040, 4367, 1457, 2186, 5028, 4947, 5028, 2879, 5030, 6481, 5995, 5996, 1649, 1198, 2166, 460, 3650, 5108, 4388, 2935, 6181, 6181, 2112, 6379, 4437, 4460, 5840, 11, 2044, 6537, 6538, 2872, 6400, 2684, 506, 1967, 2049, 2031, 2112, 4299, 4299, 4299]\n", "2025-03-02 17:32:38,061 INFO llm job done, time cost: 2.512s\n", "2025-03-02 17:32:38,062 DEBUG speech_tokens: len: 331  data: [2032, 4131, 5832, 5862, 5838, 5835, 1542, 2112, 5650, 2259, 4448, 5643, 5904, 5906, 5908, 5907, 5333, 4574, 2858, 5777, 4372, 4370, 3719, 4753, 5645, 6373, 1535, 5182, 3462, 4433, 4461, 4912, 5506, 5157, 4996, 2574, 2891, 5005, 1191, 4429, 5428, 3488, 3412, 5670, 5592, 1304, 2870, 5086, 5586, 5345, 713, 641, 2733, 4509, 3564, 2132, 4316, 6258, 6316, 6074, 233, 143, 1517, 1801, 6465, 6534, 3488, 4880, 5688, 6261, 5649, 4676, 4650, 5100, 2266, 3962, 65, 1463, 8, 159, 73, 2252, 4437, 6455, 6377, 4420, 4420, 5367, 4473, 4503, 5177, 806, 3718, 801, 1376, 2104, 2011, 4370, 6559, 6560, 2186, 2059, 2031, 3888, 5916, 5919, 5838, 5838, 5838, 2922, 4299, 6486, 1725, 5853, 4375, 6158, 6534, 6534, 2297, 4751, 4886, 4913, 4992, 4858, 3640, 5089, 5996, 2924, 66, 65, 68, 2094, 4676, 2229, 5102, 5777, 4803, 4416, 4672, 4432, 4407, 321, 1295, 1286, 1286, 1286, 2032, 4218, 5838, 5919, 3651, 1869, 2031, 1943, 2186, 3725, 4672, 6377, 5643, 4439, 4423, 5205, 5644, 5645, 4447, 4422, 6261, 2475, 2243, 802, 1284, 5562, 4095, 5828, 1457, 4288, 6534, 6538, 6534, 1457, 1457, 4373, 2186, 2113, 1303, 3888, 3645, 1461, 4218, 1587, 3883, 3812, 6077, 2690, 1403, 1448, 2081, 4435, 5483, 2220, 4994, 3266, 6259, 2508, 3069, 2891, 5777, 5773, 4920, 4752, 3483, 5048, 5046, 5772, 5769, 6077, 1463, 1505, 1046, 731, 64, 2175, 2247, 4704, 501, 287, 8, 1638, 2130, 4317, 3832, 5100, 5073, 2753, 1932, 63, 140, 299, 542, 1028, 1756, 4299, 4218, 4218, 654, 2841, 6327, 5266, 5192, 59, 6149, 6148, 2243, 317, 380, 299, 1975, 2112, 4299, 4299, 6486, 6486, 4218, 3975, 6486, 4218, 3894, 4485, 4513, 4434, 4580, 5752, 2870, 1937, 1937, 1928, 701, 5777, 6500, 3988, 1937, 1454, 3644, 5101, 4944, 5028, 2769, 5023, 5509, 6239, 3971, 1649, 3863, 1440, 2139, 703, 5915, 5189, 4388, 5934, 5934, 3981, 4299, 4299, 6405, 6405, 6486, 6486, 6486, 3894, 5832, 5835, 3975, 4299, 4218, 6378, 6373, 4520, 5914, 734, 1801, 4251, 6534, 6537, 2864, 6401, 1235, 1949, 1945, 4299, 6486]\n", "2025-03-02 17:32:38,233 INFO llm job done, time cost: 2.685s\n", "2025-03-02 17:32:38,234 DEBUG speech_tokens: len: 342  data: [2031, 4299, 6486, 6078, 5832, 5832, 5832, 5832, 3888, 4299, 2139, 5648, 4419, 4448, 5644, 6372, 3719, 3963, 3231, 4487, 4493, 5774, 6505, 6532, 3644, 803, 5238, 5482, 6131, 6130, 3963, 2996, 2834, 4913, 4940, 5668, 5668, 5750, 5506, 5506, 2032, 4299, 4218, 6075, 5832, 5832, 5832, 3972, 4299, 5838, 5239, 5482, 2314, 5089, 1200, 2250, 5752, 5672, 5589, 6318, 1952, 656, 2894, 4852, 5587, 5587, 4601, 2864, 707, 716, 1448, 2096, 2092, 2004, 4509, 1296, 584, 3590, 5774, 6289, 6315, 5344, 2411, 737, 1829, 2071, 6534, 578, 5672, 5612, 4797, 4074, 6379, 4678, 4749, 2344, 1046, 3650, 2924, 804, 2243, 2252, 5239, 6131, 2232, 4412, 5122, 5230, 4581, 4501, 3745, 3799, 80, 647, 1286, 1843, 2068, 4373, 6559, 6558, 6557, 1454, 2186, 2077, 2112, 4299, 6486, 6486, 6486, 6162, 6159, 6486, 6486, 4299, 3666, 4403, 2513, 6534, 4356, 4565, 2510, 5614, 5641, 4911, 5019, 6070, 3642, 5053, 5269, 2303, 72, 56, 808, 2004, 4435, 2563, 5534, 6261, 5532, 5160, 4513, 4513, 4513, 2490, 4211, 1052, 1295, 1987, 2031, 4299, 6486, 6486, 6405, 2031, 4129, 4130, 3968, 4431, 4753, 4674, 4523, 4393, 5205, 4429, 4671, 4492, 2937, 6093, 2243, 803, 2139, 5488, 4104, 6552, 5830, 2186, 4288, 6453, 6426, 6480, 2142, 2105, 1448, 1454, 3641, 4373, 6559, 4300, 2031, 3888, 5103, 5130, 2916, 1458, 2112, 5850, 4537, 4942, 3590, 701, 389, 4408, 4597, 2625, 5074, 6181, 6178, 3237, 4752, 5096, 5093, 5777, 5776, 6504, 4317, 1032, 5565, 3348, 4956, 5061, 6501, 4311, 4780, 2921, 776, 1775, 2, 876, 1032, 2274, 4947, 2760, 539, 8, 1560, 4317, 6019, 4992, 2590, 306, 56, 1452, 1410, 654, 3402, 5195, 5272, 2357, 62, 3722, 5986, 2260, 2324, 551, 1271, 1972, 4299, 6486, 6486, 6486, 4299, 6078, 5832, 5835, 5835, 5838, 2112, 3165, 4512, 4757, 4434, 4571, 5672, 2897, 1451, 3880, 1919, 608, 6506, 5774, 1828, 2099, 2186, 3644, 5028, 5028, 4957, 6485, 6482, 6238, 6026, 3866, 1927, 2166, 1136, 4379, 5856, 5938, 4191, 4437, 4540, 5107, 101, 1802, 1828, 6537, 4329, 5752, 4871, 509, 1319, 1312, 2112, 6486, 6486, 6486, 6486, 6486, 6486, 6078, 5832, 5832]\n", "2025-03-02 17:32:38,244 INFO yield speech index:0, len 10.68, rtf 0.297,  cost 3.167s,  all cost time 3.167s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:03<00:00,  3.17s/it]\n", "2025-03-02 17:32:38,358 INFO yield speech index:0, len 10.64, rtf 0.298,  cost 3.172s,  all cost time 3.172s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:03<00:00,  3.17s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 9 进度：1\n", "任务 9 完成，生成 1 个片段\n", "任务 12 进度：1\n", "任务 12 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:38,861 INFO yield speech index:0, len 11.08, rtf 0.348,  cost 3.855s,  all cost time 3.855s\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:03<00:00,  3.86s/it]\n", "2025-03-02 17:32:39,012 INFO yield speech index:0, len 11.12, rtf 0.331,  cost 3.678s,  all cost time 3.678s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:03<00:00,  3.68s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：1\n", "任务 7 完成，生成 1 个片段\n", "任务 16 进度：1\n", "任务 16 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:39,133 INFO yield speech index:0, len 11.24, rtf 0.323,  cost 3.627s,  all cost time 3.627s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:03<00:00,  3.63s/it]\n", "2025-03-02 17:32:39,229 INFO yield speech index:0, len 11.28, rtf 0.384,  cost 4.326s,  all cost time 4.326s\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:04<00:00,  4.33s/it]\n", "2025-03-02 17:32:39,297 INFO llm job done, time cost: 3.751s\n", "2025-03-02 17:32:39,298 DEBUG speech_tokens: len: 405  data: [4299, 2031, 3888, 3645, 3645, 5835, 5835, 5838, 5838, 3651, 1869, 2112, 2490, 4672, 4447, 4437, 4510, 4671, 5176, 2990, 2990, 803, 803, 803, 1529, 1525, 5904, 5175, 4564, 2861, 6506, 5777, 6533, 4372, 2180, 5167, 4510, 4753, 4672, 3465, 1532, 803, 5177, 803, 1759, 2112, 4299, 6486, 4299, 6486, 6486, 6486, 6486, 6486, 5919, 5919, 5916, 5838, 5919, 5835, 5835, 3648, 1545, 3894, 4482, 4996, 4915, 2233, 5089, 5097, 1191, 2493, 5266, 6400, 491, 3402, 6075, 1945, 1949, 2891, 5344, 5343, 4610, 725, 1373, 885, 4755, 3240, 593, 674, 5693, 5317, 6316, 6316, 2420, 116, 41, 1507, 1480, 4251, 6537, 4356, 2756, 4955, 4880, 4473, 1887, 4191, 6377, 5405, 4686, 4857, 4777, 3800, 803, 1463, 3653, 62, 1029, 2252, 2324, 4428, 4997, 4671, 2225, 4394, 5366, 5123, 4420, 5177, 803, 3802, 79, 1376, 2014, 1853, 1856, 6557, 6557, 6557, 2183, 2096, 4299, 6486, 6486, 6486, 6486, 6486, 3891, 5832, 5835, 5835, 5835, 3648, 2112, 2031, 6018, 4393, 5185, 3520, 6534, 6534, 4772, 2252, 2234, 47, 752, 2942, 1286, 2006, 1285, 5667, 5019, 5019, 3158, 3640, 3643, 5051, 5995, 5269, 2303, 71, 2094, 73, 65, 65, 794, 1526, 1528, 2112, 2058, 5647, 4676, 4695, 4994, 4966, 6261, 4722, 4428, 5402, 5405, 5402, 4675, 4675, 3228, 1052, 1286, 1286, 2005, 2032, 1861, 4129, 3887, 1700, 1862, 1781, 4443, 4510, 4429, 4428, 2234, 4412, 4396, 5151, 4752, 4428, 4442, 4420, 5937, 3750, 2251, 29, 802, 2094, 6216, 6291, 6552, 5097, 5831, 2102, 6534, 6534, 6534, 2182, 2102, 2075, 1373, 1454, 5828, 5827, 2096, 4299, 4299, 6486, 6486, 6486, 6486, 3891, 5832, 5832, 5832, 3645, 1788, 1506, 5180, 5189, 5591, 4319, 3644, 626, 806, 4431, 5405, 4407, 2807, 5452, 6262, 4677, 3393, 5066, 5044, 6258, 1032, 6295, 3348, 2676, 5038, 5038, 6501, 6501, 6498, 5428, 3650, 740, 1505, 1019, 2216, 1923, 786, 4542, 4785, 350, 62, 1560, 4075, 4068, 4832, 5073, 5020, 79, 2094, 73, 2246, 137, 56, 1514, 1028, 2139, 4299, 654, 654, 2757, 6084, 5267, 3005, 251, 805, 3962, 3961, 56, 308, 2000, 2112, 6486, 4299, 4218, 3645, 5835, 5835, 5835, 5838, 5838, 5838, 5838, 5838, 5838, 2922, 3975, 4299, 2922, 4404, 4753, 4918, 4525, 5509, 5053, 2828, 3721, 884, 689, 3506, 3992, 6141, 1855, 1451, 5830, 2760, 5028, 2859, 5755, 6400, 6158, 5996, 6059, 2169, 4353, 712, 4457, 4385, 2934, 6261, 1833, 5648, 2223, 4536, 5105, 1478, 1804, 6537, 6534, 6534, 5024, 6320, 1238, 1958, 2040, 4299, 4299, 6486, 6486, 6486, 6486, 6486, 6486, 4218]\n", "2025-03-02 17:32:39,319 INFO yield speech index:0, len 11.24, rtf 0.337,  cost 3.788s,  all cost time 3.788s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:03<00:00,  3.79s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 22 进度：1\n", "任务 22 完成，生成 1 个片段\n", "任务 4 进度：1\n", "任务 4 完成，生成 1 个片段\n", "任务 24 进度：1\n", "任务 24 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:39,453 INFO yield speech index:0, len 11.44, rtf 0.401,  cost 4.586s,  all cost time 4.586s\n", "\n", "\n", "100%|██████████| 1/1 [00:04<00:00,  4.59s/it]\n", "2025-03-02 17:32:39,463 INFO yield speech index:0, len 11.52, rtf 0.362,  cost 4.167s,  all cost time 4.167s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:04<00:00,  4.17s/it]\n", "2025-03-02 17:32:39,628 INFO yield speech index:0, len 11.56, rtf 0.378,  cost 4.367s,  all cost time 4.367s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:04<00:00,  4.38s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 3 进度：1\n", "任务 3 完成，生成 1 个片段\n", "任务 15 进度：1\n", "任务 15 完成，生成 1 个片段\n", "任务 14 进度：1\n", "任务 14 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:40,752 INFO yield speech index:0, len 11.64, rtf 0.508,  cost 5.919s,  all cost time 5.919s\n", "\n", "100%|██████████| 1/1 [00:05<00:00,  5.92s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 2 进度：1\n", "任务 2 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:41,008 INFO yield speech index:0, len 11.68, rtf 0.480,  cost 5.602s,  all cost time 5.602s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:05<00:00,  5.61s/it]\u001b[A\u001b[A\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 18 进度：1\n", "任务 18 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:41,706 INFO yield speech index:0, len 11.80, rtf 0.588,  cost 6.941s,  all cost time 6.941s\n", "100%|██████████| 1/1 [00:06<00:00,  6.94s/it]\n", "2025-03-02 17:32:41,855 INFO yield speech index:0, len 12.04, rtf 0.551,  cost 6.632s,  all cost time 6.632s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:06<00:00,  6.64s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 0 进度：1\n", "任务 0 完成，生成 1 个片段\n", "任务 13 进度：1\n", "任务 13 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:41,970 INFO yield speech index:0, len 12.04, rtf 0.584,  cost 7.035s,  all cost time 7.035s\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:07<00:00,  7.04s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 5 进度：1\n", "任务 5 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:42,181 INFO yield speech index:0, len 11.96, rtf 0.564,  cost 6.741s,  all cost time 6.741s\n", "100%|██████████| 1/1 [00:06<00:00,  6.74s/it]\n", "2025-03-02 17:32:42,191 INFO yield speech index:0, len 12.20, rtf 0.592,  cost 7.220s,  all cost time 7.220s\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:07<00:00,  7.22s/it]\n", "2025-03-02 17:32:42,343 INFO yield speech index:0, len 12.36, rtf 0.591,  cost 7.302s,  all cost time 7.302s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:07<00:00,  7.31s/it]\n", "2025-03-02 17:32:42,353 INFO yield speech index:0, len 12.20, rtf 0.563,  cost 6.865s,  all cost time 6.865s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:06<00:00,  6.87s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 19 进度：1\n", "任务 19 完成，生成 1 个片段\n", "任务 6 进度：1\n", "任务 6 完成，生成 1 个片段\n", "任务 8 进度：1\n", "任务 8 完成，生成 1 个片段\n", "任务 21 进度：1\n", "任务 21 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:42,446 INFO yield speech index:0, len 12.68, rtf 0.546,  cost 6.927s,  all cost time 6.927s\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:06<00:00,  6.93s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 23 进度：1\n", "任务 23 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:42,943 INFO yield speech index:0, len 13.24, rtf 0.572,  cost 7.573s,  all cost time 7.573s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:07<00:00,  7.58s/it]\u001b[A\n", "2025-03-02 17:32:42,962 INFO yield speech index:0, len 13.68, rtf 0.571,  cost 7.815s,  all cost time 7.815s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:07<00:00,  7.82s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 17 进度：1\n", "任务 17 完成，生成 1 个片段\n", "任务 11 进度：1\n", "任务 11 完成，生成 1 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:43,145 INFO yield speech index:0, len 16.20, rtf 0.496,  cost 8.033s,  all cost time 8.033s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:08<00:00,  8.04s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 10 进度：1\n", "任务 10 完成，生成 1 个片段\n", "--- 8.427155256271362 seconds ---\n"]}], "source": ["start_time = time.time()\n", "await test_concurrent_instruct(25, semaphore_limit=25)\n", "print(\"--- %s seconds ---\" % (time.time() - start_time))"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]2025-03-02 17:32:43,800 INFO synthesis text 这是任务零，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:32:43,836 INFO synthesis text 这是任务一，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:32:44,160 INFO yield speech index:0, len 0.44, rtf 0.738,  cost 0.325s,  all cost time 0.325s\n", "2025-03-02 17:32:44,161 DEBUG multiples: 1.20, peer_chunk_token_num: 15\n", "2025-03-02 17:32:44,170 INFO yield speech index:0, len 0.44, rtf 0.843,  cost 0.371s,  all cost time 0.371s\n", "2025-03-02 17:32:44,171 DEBUG multiples: 0.88, peer_chunk_token_num: 15\n", "2025-03-02 17:32:44,283 INFO yield speech index:1, len 0.60, rtf 0.204,  cost 0.122s,  all cost time 0.448s\n", "2025-03-02 17:32:44,284 DEBUG multiples: 4.06, peer_chunk_token_num: 30\n", "2025-03-02 17:32:44,320 INFO yield speech index:1, len 0.60, rtf 0.249,  cost 0.149s,  all cost time 0.521s\n", "2025-03-02 17:32:44,321 DEBUG multiples: 3.12, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 1 进度：1\n", "任务 0 进度：1\n", "任务 1 进度：2\n", "任务 0 进度：2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:44,432 INFO yield speech index:2, len 0.60, rtf 0.185,  cost 0.111s,  all cost time 0.632s\n", "2025-03-02 17:32:44,433 DEBUG multiples: 6.30, peer_chunk_token_num: 30\n", "2025-03-02 17:32:44,500 INFO yield speech index:2, len 1.20, rtf 0.180,  cost 0.216s,  all cost time 0.664s\n", "2025-03-02 17:32:44,501 DEBUG multiples: 8.76, peer_chunk_token_num: 45\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 0 进度：3\n", "任务 1 进度：3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:44,634 INFO yield speech index:3, len 1.20, rtf 0.167,  cost 0.201s,  all cost time 0.834s\n", "2025-03-02 17:32:44,634 DEBUG multiples: 11.35, peer_chunk_token_num: 45\n", "2025-03-02 17:32:44,792 INFO yield speech index:3, len 1.80, rtf 0.162,  cost 0.291s,  all cost time 0.956s\n", "2025-03-02 17:32:44,793 DEBUG multiples: 14.58, peer_chunk_token_num: 60\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 0 进度：4\n", "任务 1 进度：4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:44,918 INFO yield speech index:4, len 1.80, rtf 0.158,  cost 0.284s,  all cost time 1.118s\n", "2025-03-02 17:32:44,919 DEBUG multiples: 17.50, peer_chunk_token_num: 60\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 0 进度：5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:45,208 INFO yield speech index:4, len 2.40, rtf 0.173,  cost 0.415s,  all cost time 1.372s\n", "2025-03-02 17:32:45,208 DEBUG multiples: 20.00, peer_chunk_token_num: 75\n", "2025-03-02 17:32:45,343 INFO yield speech index:5, len 2.40, rtf 0.177,  cost 0.424s,  all cost time 1.543s\n", "2025-03-02 17:32:45,344 DEBUG multiples: 22.96, peer_chunk_token_num: 75\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 1 进度：5\n", "任务 0 进度：6\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:45,755 INFO yield speech index:5, len 3.00, rtf 0.182,  cost 0.546s,  all cost time 1.919s\n", "2025-03-02 17:32:45,756 DEBUG multiples: 24.85, peer_chunk_token_num: 90\n", "2025-03-02 17:32:45,860 INFO yield speech index:6, len 3.00, rtf 0.172,  cost 0.516s,  all cost time 2.060s\n", "2025-03-02 17:32:45,860 DEBUG multiples: 28.55, peer_chunk_token_num: 90\n", "2025-03-02 17:32:45,873 INFO llm job done, time cost: 2.034s\n", "2025-03-02 17:32:45,873 DEBUG speech_tokens: len: 283  data: [1947, 3645, 5832, 5835, 3648, 1788, 2112, 6377, 2250, 4520, 5410, 5652, 6149, 6148, 6390, 4447, 4484, 4964, 5777, 4372, 6559, 3638, 1693, 3798, 143, 395, 593, 2051, 6259, 5448, 4299, 5835, 5919, 5838, 3651, 2112, 5136, 4833, 4752, 2585, 5098, 468, 2250, 5023, 5753, 3412, 6318, 1949, 656, 2894, 5345, 5588, 2423, 716, 719, 1448, 1765, 2139, 4755, 3321, 1322, 3587, 6019, 6316, 3149, 116, 1154, 6537, 686, 3491, 4880, 1563, 4920, 4434, 4606, 2260, 2, 737, 153, 2243, 2243, 5483, 5645, 2234, 4412, 5122, 5367, 5148, 4528, 2990, 3802, 77, 1133, 1376, 1700, 2156, 2186, 4373, 4370, 1457, 1445, 2056, 3645, 3645, 3648, 1461, 2112, 1806, 5853, 4376, 4780, 6537, 6534, 4565, 4400, 3428, 5614, 4992, 5344, 3639, 5078, 5918, 157, 38, 2258, 4200, 4675, 2483, 5776, 5776, 5073, 4429, 4435, 4407, 1294, 1295, 1133, 1943, 1942, 4130, 2996, 4753, 5402, 73, 4411, 5124, 5401, 4419, 4473, 5853, 2495, 1775, 3471, 5565, 5580, 5827, 2186, 4288, 6507, 6534, 4359, 2183, 1373, 716, 2912, 2031, 3888, 3645, 3648, 3648, 1704, 2112, 3693, 4450, 5509, 5048, 1403, 677, 2983, 4753, 4515, 2807, 6259, 3912, 5250, 2909, 5048, 5773, 1833, 5484, 3564, 5047, 5046, 5772, 6498, 3485, 1463, 47, 3961, 272, 2243, 1923, 2490, 4380, 249, 44, 44, 1884, 4068, 5074, 4534, 1044, 116, 1126, 489, 648, 5598, 5996, 2195, 158, 4205, 4204, 2243, 2342, 713, 596, 2056, 1702, 3645, 3648, 3648, 3648, 5835, 3648, 3729, 3648, 3729, 1788, 2112, 5109, 4512, 4759, 2337, 4537, 5029, 2828, 3961, 641, 2780, 3506, 4232, 6147, 4042, 2099, 3644, 5028, 5028, 5028, 5027, 6238, 6238, 3836, 1676, 1199, 1449, 1440, 191, 5840, 2206, 4074, 5647, 4442, 5105, 254, 1883, 6465, 6534, 677, 3404, 2684, 509, 2038, 2112]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 1 进度：6\n", "任务 0 进度：7\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:46,095 INFO yield speech index:7, len 1.28, rtf 0.183,  cost 0.234s,  all cost time 2.295s\n", "100%|██████████| 1/1 [00:02<00:00,  2.30s/it]\n", "2025-03-02 17:32:46,168 INFO llm job done, time cost: 2.328s\n", "2025-03-02 17:32:46,169 DEBUG speech_tokens: len: 328  data: [4131, 5835, 5835, 3732, 1788, 2004, 4673, 2313, 4527, 4752, 4671, 5176, 5177, 5908, 5166, 4604, 4592, 5774, 6506, 5776, 5831, 2912, 3077, 1294, 1267, 4939, 5020, 5020, 5020, 5019, 5019, 1286, 1735, 5211, 5563, 2322, 5090, 5095, 1032, 4565, 5510, 2756, 3412, 6318, 4142, 2030, 710, 5342, 5587, 227, 626, 641, 1113, 4836, 3483, 584, 674, 6506, 5693, 6179, 6262, 4128, 6070, 233, 113, 1523, 2044, 6537, 6534, 5675, 2693, 4637, 3750, 6379, 4676, 4695, 4769, 884, 65, 3650, 35, 867, 64, 146, 5238, 4590, 4509, 2333, 5123, 5366, 5365, 4446, 4528, 3071, 1532, 3799, 809, 1295, 1375, 1852, 2093, 6559, 5829, 5102, 1448, 1303, 3159, 3645, 5943, 5943, 5832, 1539, 4299, 5853, 5850, 4456, 4708, 4871, 4871, 5591, 2759, 6462, 6534, 6510, 6456, 6534, 4852, 4519, 4439, 2963, 2966, 1286, 1978, 1285, 5073, 5073, 5021, 2834, 3803, 3644, 5830, 5084, 5752, 5914, 2357, 305, 156, 72, 59, 76, 2175, 2004, 6377, 2499, 5021, 5047, 6262, 4803, 4432, 4513, 4675, 2499, 5669, 2753, 566, 2015, 2005, 1861, 6316, 6317, 4130, 2024, 3725, 4509, 4509, 4752, 4428, 2261, 4442, 4394, 5855, 5882, 5179, 4590, 4752, 4448, 4439, 5850, 6177, 2502, 2261, 801, 2031, 5001, 6051, 5581, 5831, 4373, 4373, 6453, 6537, 6513, 2077, 2186, 2186, 2186, 2915, 705, 4131, 5832, 3648, 1626, 1788, 3801, 6062, 6157, 2771, 3590, 2186, 710, 875, 4509, 4433, 294, 2834, 6181, 6262, 2733, 3144, 2864, 2860, 5774, 3339, 2733, 5319, 2673, 5047, 5044, 6502, 6255, 668, 3647, 20, 3962, 2, 2243, 2166, 2217, 4947, 2698, 35, 8, 1638, 4072, 6501, 4561, 5019, 79, 306, 29, 724, 1410, 2841, 2769, 5680, 5508, 5917, 2276, 2330, 5986, 5986, 4430, 2576, 299, 1729, 5832, 5916, 3732, 2112, 5136, 4513, 4513, 4677, 4570, 5509, 5057, 3638, 4124, 4124, 1676, 680, 3590, 5774, 3853, 1828, 2180, 3644, 2887, 5028, 5028, 5028, 5038, 5752, 6239, 6023, 6050, 2163, 2163, 5429, 5837, 2208, 6018, 4200, 6373, 4484, 5108, 128, 1804, 6537, 6534, 2837, 6401, 3413, 1958, 2028, 1704, 4299, 4299]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 0 进度：8\n", "任务 0 完成，生成 8 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:46,393 INFO yield speech index:6, len 3.68, rtf 0.173,  cost 0.637s,  all cost time 2.557s\n", "100%|██████████| 1/1 [00:02<00:00,  2.56s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 1 进度：7\n", "任务 1 完成，生成 7 个片段\n", "--- 2.631807804107666 seconds ---\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["start_time = time.time()\n", "await test_concurrent_instruct(2, semaphore_limit=5, stream=True)\n", "print(\"--- %s seconds ---\" % (time.time() - start_time))"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]2025-03-02 17:32:46,423 INFO synthesis text 这是任务零，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:32:46,444 INFO synthesis text 这是任务一，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\u001b[A2025-03-02 17:32:46,461 INFO synthesis text 这是任务二，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\u001b[A\u001b[A2025-03-02 17:32:46,479 INFO synthesis text 这是任务三，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A2025-03-02 17:32:46,500 INFO synthesis text 这是任务四，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:32:46,945 INFO yield speech index:0, len 0.44, rtf 1.059,  cost 0.466s,  all cost time 0.466s\n", "2025-03-02 17:32:46,946 DEBUG multiples: 0.44, peer_chunk_token_num: 15\n", "2025-03-02 17:32:46,967 INFO yield speech index:0, len 0.44, rtf 1.151,  cost 0.506s,  all cost time 0.506s\n", "2025-03-02 17:32:46,967 DEBUG multiples: 0.32, peer_chunk_token_num: 15\n", "2025-03-02 17:32:46,993 INFO yield speech index:0, len 0.44, rtf 1.248,  cost 0.549s,  all cost time 0.549s\n", "2025-03-02 17:32:46,994 DEBUG multiples: 0.23, peer_chunk_token_num: 15\n", "2025-03-02 17:32:46,995 INFO yield speech index:0, len 0.44, rtf 1.300,  cost 0.572s,  all cost time 0.572s\n", "2025-03-02 17:32:46,996 DEBUG multiples: 0.23, peer_chunk_token_num: 15\n", "2025-03-02 17:32:47,018 INFO yield speech index:0, len 0.44, rtf 1.177,  cost 0.518s,  all cost time 0.518s\n", "2025-03-02 17:32:47,019 DEBUG multiples: 0.28, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 3 进度：1\n", "任务 2 进度：1\n", "任务 1 进度：1\n", "任务 0 进度：1\n", "任务 4 进度：1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:47,163 INFO yield speech index:1, len 0.60, rtf 0.362,  cost 0.217s,  all cost time 0.684s\n", "2025-03-02 17:32:47,164 DEBUG multiples: 1.79, peer_chunk_token_num: 15\n", "2025-03-02 17:32:47,280 INFO yield speech index:1, len 0.60, rtf 0.521,  cost 0.312s,  all cost time 0.819s\n", "2025-03-02 17:32:47,281 DEBUG multiples: 1.12, peer_chunk_token_num: 15\n", "2025-03-02 17:32:47,282 INFO yield speech index:1, len 0.60, rtf 0.479,  cost 0.288s,  all cost time 0.837s\n", "2025-03-02 17:32:47,282 DEBUG multiples: 1.09, peer_chunk_token_num: 15\n", "2025-03-02 17:32:47,349 INFO yield speech index:1, len 0.60, rtf 0.590,  cost 0.354s,  all cost time 0.926s\n", "2025-03-02 17:32:47,350 DEBUG multiples: 0.84, peer_chunk_token_num: 15\n", "2025-03-02 17:32:47,353 INFO yield speech index:1, len 0.60, rtf 0.556,  cost 0.334s,  all cost time 0.852s\n", "2025-03-02 17:32:47,353 DEBUG multiples: 1.00, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 3 进度：2\n", "任务 2 进度：2\n", "任务 1 进度：2\n", "任务 0 进度：2\n", "任务 4 进度：2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:47,510 INFO yield speech index:2, len 0.60, rtf 0.578,  cost 0.347s,  all cost time 1.031s\n", "2025-03-02 17:32:47,511 DEBUG multiples: 2.51, peer_chunk_token_num: 15\n", "2025-03-02 17:32:47,565 INFO yield speech index:2, len 0.60, rtf 0.471,  cost 0.283s,  all cost time 1.121s\n", "2025-03-02 17:32:47,566 DEBUG multiples: 2.10, peer_chunk_token_num: 30\n", "2025-03-02 17:32:47,568 INFO yield speech index:2, len 0.60, rtf 0.479,  cost 0.287s,  all cost time 1.107s\n", "2025-03-02 17:32:47,568 DEBUG multiples: 2.11, peer_chunk_token_num: 30\n", "2025-03-02 17:32:47,693 INFO yield speech index:2, len 0.60, rtf 0.567,  cost 0.340s,  all cost time 1.193s\n", "2025-03-02 17:32:47,694 DEBUG multiples: 1.73, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 3 进度：3\n", "任务 1 进度：3\n", "任务 2 进度：3\n", "任务 4 进度：3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:47,717 INFO yield speech index:2, len 0.60, rtf 0.612,  cost 0.367s,  all cost time 1.294s\n", "2025-03-02 17:32:47,718 DEBUG multiples: 1.46, peer_chunk_token_num: 15\n", "2025-03-02 17:32:47,895 INFO yield speech index:3, len 0.60, rtf 0.639,  cost 0.384s,  all cost time 1.415s\n", "2025-03-02 17:32:47,895 DEBUG multiples: 3.03, peer_chunk_token_num: 30\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 0 进度：3\n", "任务 3 进度：4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:47,954 INFO yield speech index:3, len 1.20, rtf 0.324,  cost 0.388s,  all cost time 1.510s\n", "2025-03-02 17:32:47,955 DEBUG multiples: 4.28, peer_chunk_token_num: 45\n", "2025-03-02 17:32:47,984 INFO yield speech index:3, len 1.20, rtf 0.346,  cost 0.415s,  all cost time 1.523s\n", "2025-03-02 17:32:47,984 DEBUG multiples: 4.15, peer_chunk_token_num: 45\n", "2025-03-02 17:32:48,068 INFO yield speech index:3, len 0.60, rtf 0.625,  cost 0.375s,  all cost time 1.568s\n", "2025-03-02 17:32:48,069 DEBUG multiples: 2.33, peer_chunk_token_num: 45\n", "2025-03-02 17:32:48,084 INFO yield speech index:3, len 0.60, rtf 0.611,  cost 0.366s,  all cost time 1.661s\n", "2025-03-02 17:32:48,085 DEBUG multiples: 2.08, peer_chunk_token_num: 45\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 1 进度：4\n", "任务 2 进度：4\n", "任务 4 进度：4\n", "任务 0 进度：4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:48,347 INFO yield speech index:4, len 1.20, rtf 0.376,  cost 0.452s,  all cost time 1.868s\n", "2025-03-02 17:32:48,348 DEBUG multiples: 4.90, peer_chunk_token_num: 60\n", "2025-03-02 17:32:48,505 INFO yield speech index:4, len 1.80, rtf 0.289,  cost 0.521s,  all cost time 2.045s\n", "2025-03-02 17:32:48,506 DEBUG multiples: 7.04, peer_chunk_token_num: 60\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 3 进度：5\n", "任务 2 进度：5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:48,555 INFO yield speech index:4, len 1.80, rtf 0.334,  cost 0.601s,  all cost time 2.111s\n", "2025-03-02 17:32:48,556 DEBUG multiples: 6.71, peer_chunk_token_num: 60\n", "2025-03-02 17:32:48,613 INFO yield speech index:4, len 1.80, rtf 0.302,  cost 0.544s,  all cost time 2.113s\n", "2025-03-02 17:32:48,614 DEBUG multiples: 5.18, peer_chunk_token_num: 75\n", "2025-03-02 17:32:48,617 INFO yield speech index:4, len 1.80, rtf 0.296,  cost 0.532s,  all cost time 2.194s\n", "2025-03-02 17:32:48,618 DEBUG multiples: 4.95, peer_chunk_token_num: 75\n", "2025-03-02 17:32:48,681 INFO yield speech index:5, len 2.40, rtf 0.139,  cost 0.333s,  all cost time 2.202s\n", "2025-03-02 17:32:48,681 DEBUG multiples: 10.74, peer_chunk_token_num: 75\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 1 进度：5\n", "任务 4 进度：5\n", "任务 0 进度：5\n", "任务 3 进度：6\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:49,250 INFO yield speech index:5, len 3.00, rtf 0.212,  cost 0.636s,  all cost time 2.750s\n", "2025-03-02 17:32:49,250 DEBUG multiples: 10.01, peer_chunk_token_num: 90\n", "2025-03-02 17:32:49,254 INFO yield speech index:5, len 3.00, rtf 0.212,  cost 0.636s,  all cost time 2.831s\n", "2025-03-02 17:32:49,254 DEBUG multiples: 9.72, peer_chunk_token_num: 90\n", "2025-03-02 17:32:49,259 INFO yield speech index:5, len 2.40, rtf 0.293,  cost 0.703s,  all cost time 2.815s\n", "2025-03-02 17:32:49,259 DEBUG multiples: 9.69, peer_chunk_token_num: 75\n", "2025-03-02 17:32:49,264 INFO yield speech index:5, len 2.40, rtf 0.316,  cost 0.758s,  all cost time 2.803s\n", "2025-03-02 17:32:49,265 DEBUG multiples: 9.70, peer_chunk_token_num: 75\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 4 进度：6\n", "任务 0 进度：6\n", "任务 1 进度：6\n", "任务 2 进度：6\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:49,452 INFO yield speech index:6, len 3.00, rtf 0.257,  cost 0.771s,  all cost time 2.973s\n", "2025-03-02 17:32:49,453 DEBUG multiples: 14.56, peer_chunk_token_num: 90\n", "2025-03-02 17:32:49,614 INFO llm job done, time cost: 3.110s\n", "2025-03-02 17:32:49,614 DEBUG speech_tokens: len: 269  data: [1701, 5832, 5862, 5943, 5835, 3648, 2112, 2490, 2331, 4520, 4428, 4509, 2989, 5180, 3722, 3720, 5907, 4523, 4807, 5777, 6506, 6560, 5831, 1247, 1976, 6401, 6481, 6239, 1649, 1661, 5170, 4833, 4752, 389, 2903, 234, 315, 4700, 6401, 572, 5589, 6318, 4132, 1223, 683, 2666, 5830, 5345, 722, 641, 1842, 5565, 576, 692, 3503, 4102, 4129, 224, 41, 992, 4350, 6537, 2756, 4955, 4880, 3828, 3471, 4676, 4452, 4615, 5906, 29, 3653, 35, 72, 56, 2254, 4834, 4671, 65, 2225, 5153, 5233, 5259, 5176, 5905, 79, 3563, 1376, 1616, 4289, 4373, 6559, 3644, 1457, 1299, 3645, 5940, 5943, 3675, 1869, 4218, 3666, 5104, 6157, 2548, 6534, 3528, 4511, 4415, 4643, 3455, 4912, 5019, 5020, 4124, 5101, 4781, 4463, 159, 74, 59, 1860, 5651, 2265, 5021, 6505, 5532, 4425, 4510, 4433, 4434, 2510, 1295, 2104, 1943, 2105, 1781, 4512, 4510, 36, 4439, 4477, 5166, 4671, 4447, 5934, 4393, 2243, 802, 1284, 5565, 5581, 5828, 3644, 4276, 6534, 6534, 4320, 1457, 1457, 2180, 3799, 6070, 5996, 5591, 2861, 4319, 1457, 704, 149, 4434, 4513, 2247, 5021, 6182, 6260, 312, 2331, 2891, 5048, 5773, 1824, 4755, 2763, 5065, 5775, 6498, 4790, 3647, 1586, 3962, 2192, 156, 57, 4461, 537, 386, 35, 835, 4317, 4072, 5100, 879, 68, 469, 573, 2838, 5599, 5265, 5192, 2222, 3799, 6229, 317, 2324, 623, 1756, 3888, 5832, 5862, 3648, 1869, 5109, 4753, 4677, 4567, 5752, 3620, 4124, 4115, 2864, 3590, 4097, 4096, 1937, 728, 2915, 4947, 4947, 2770, 6404, 6400, 6157, 3836, 1652, 1676, 1926, 2166, 434, 3650, 4414, 5856, 1833, 6374, 4411, 5269, 11, 2045, 2046, 6537, 1440, 6401, 3413, 1235, 1957, 4299, 6486, 4299]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 3 进度：7\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:49,951 INFO yield speech index:6, len 3.00, rtf 0.231,  cost 0.692s,  all cost time 3.507s\n", "2025-03-02 17:32:49,952 DEBUG multiples: 13.72, peer_chunk_token_num: 90\n", "2025-03-02 17:32:49,957 INFO llm job done, time cost: 3.455s\n", "2025-03-02 17:32:49,957 DEBUG speech_tokens: len: 293  data: [2031, 6075, 6102, 5916, 3975, 2031, 4755, 2395, 2333, 4671, 5652, 3961, 6148, 5177, 4520, 4493, 3503, 5777, 5801, 4373, 4372, 3644, 1454, 1693, 3799, 74, 314, 647, 1079, 6262, 5448, 4509, 4833, 2331, 5087, 5089, 225, 297, 4564, 5672, 1217, 5589, 4141, 1304, 2897, 4853, 5587, 4853, 719, 641, 2490, 3861, 1318, 3587, 6259, 6315, 5345, 113, 1091, 2126, 4278, 4347, 5672, 4879, 5529, 4200, 4433, 4659, 4606, 3799, 32, 2921, 876, 64, 56, 4437, 4833, 4752, 2261, 4421, 5204, 5962, 5260, 5342, 3881, 1692, 75, 485, 1859, 2183, 4373, 6560, 5830, 4373, 2186, 2167, 2031, 6156, 5859, 5862, 5865, 5946, 5919, 3651, 2112, 5934, 4393, 4457, 4275, 6534, 4853, 4418, 4886, 4912, 4938, 5019, 4615, 3640, 5101, 5266, 5111, 156, 73, 71, 4200, 4676, 2265, 5075, 6263, 6262, 5532, 4407, 4513, 4435, 4677, 1051, 1295, 2015, 2015, 4130, 3887, 1943, 1538, 4509, 4510, 2233, 4439, 4396, 4680, 4419, 4473, 5937, 2233, 65, 1533, 5730, 6291, 5824, 5099, 1454, 4289, 6453, 6534, 6534, 2185, 1457, 1457, 2183, 1769, 1780, 4607, 5996, 4955, 3590, 701, 1352, 2258, 4509, 4759, 2499, 5452, 5938, 5169, 2657, 5777, 5772, 1023, 5319, 3564, 5048, 5043, 5769, 6498, 5185, 734, 1721, 290, 59, 2166, 57, 2517, 539, 305, 121, 4072, 6258, 3832, 5020, 150, 67, 137, 2166, 651, 3483, 5842, 5188, 5192, 251, 77, 3799, 73, 218, 1370, 623, 1973, 2031, 3888, 5916, 5838, 5865, 5946, 2922, 1059, 4515, 4516, 2310, 4537, 4946, 5084, 3641, 4046, 3806, 1199, 716, 674, 3590, 5774, 3989, 1750, 2099, 4373, 6559, 2877, 5754, 5028, 5038, 5752, 5672, 5752, 5752, 3836, 1679, 3875, 1198, 2166, 703, 6077, 5108, 4424, 5854, 5938, 3993, 4299, 4299, 4191, 5645, 4495, 5914, 731, 1802, 2046, 6534, 6534, 5024, 3404, 506, 1238, 2029, 2112, 3888, 5913, 6183, 3672]\n", "2025-03-02 17:32:50,079 INFO yield speech index:6, len 3.00, rtf 0.271,  cost 0.814s,  all cost time 3.618s\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 1 进度：7\n", "任务 2 进度：7\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:50,232 INFO llm job done, time cost: 3.727s\n", "2025-03-02 17:32:50,233 DEBUG speech_tokens: len: 307  data: [1944, 5832, 5862, 5835, 3651, 1896, 5405, 4527, 4528, 4672, 5409, 5904, 5904, 5904, 4594, 4816, 5777, 3590, 3644, 4367, 5886, 6454, 6376, 3231, 5189, 5105, 4388, 5122, 5934, 6180, 3984, 2112, 4299, 4299, 3921, 4752, 4753, 2484, 5087, 2583, 315, 4780, 4862, 3169, 6075, 4141, 1223, 2897, 5096, 5586, 4616, 632, 1451, 2490, 5319, 1315, 698, 5774, 6181, 4128, 962, 35, 848, 2071, 6540, 1441, 5594, 4879, 4473, 1806, 6380, 4679, 4938, 4777, 883, 1532, 1466, 2222, 867, 2260, 65, 4428, 4752, 4671, 4439, 4421, 5206, 5229, 5229, 5258, 3071, 3800, 1531, 1052, 1294, 1861, 2011, 2105, 6559, 6559, 1430, 2132, 2149, 2112, 3888, 5835, 5835, 5835, 5835, 3645, 3648, 4299, 1887, 5121, 5185, 4709, 6534, 6453, 4592, 4490, 4966, 3426, 4911, 5020, 5338, 3643, 4781, 5191, 148, 72, 2258, 1851, 5651, 4677, 5750, 5776, 6504, 4749, 5239, 5405, 4407, 3482, 566, 1295, 2015, 1943, 1862, 3724, 4590, 4671, 2234, 5123, 5151, 4916, 4447, 4420, 1725, 4447, 2252, 1851, 5001, 6300, 6309, 5828, 2186, 6453, 6538, 6534, 2182, 2105, 1457, 1457, 3641, 2136, 3645, 5838, 5109, 735, 1815, 3805, 4543, 4780, 5048, 1403, 707, 2987, 4510, 4434, 3563, 6262, 6262, 4686, 3069, 5078, 5045, 6502, 3993, 4836, 4077, 2686, 5073, 5772, 6504, 5032, 5186, 14, 1019, 38, 67, 1842, 87, 4947, 565, 62, 1557, 4317, 3832, 5073, 150, 2255, 218, 1410, 651, 2838, 5356, 5917, 5111, 62, 803, 6230, 4691, 4511, 2504, 398, 1352, 1271, 2031, 3645, 3648, 5835, 5838, 5919, 5919, 3003, 1869, 5136, 5323, 4516, 2346, 4536, 4943, 716, 4124, 3880, 1694, 713, 719, 1403, 6506, 6421, 6420, 4227, 4218, 2031, 2031, 2031, 1828, 3880, 2180, 3644, 2797, 5028, 2841, 2886, 5755, 6157, 5752, 5023, 6239, 1649, 1919, 2160, 2139, 703, 5915, 2219, 5856, 1887, 4675, 4449, 5188, 2921, 848, 2044, 4269, 6537, 6534, 5024, 5591, 506, 1958, 1945, 2109, 4056, 3972, 3975, 1788]\n", "2025-03-02 17:32:50,340 INFO yield speech index:6, len 3.60, rtf 0.302,  cost 1.086s,  all cost time 3.917s\n", "2025-03-02 17:32:50,342 INFO yield speech index:6, len 3.60, rtf 0.303,  cost 1.092s,  all cost time 3.842s\n", "2025-03-02 17:32:50,343 DEBUG multiples: 12.94, peer_chunk_token_num: 105\n", "2025-03-02 17:32:50,438 INFO llm job done, time cost: 3.934s\n", "2025-03-02 17:32:50,438 DEBUG speech_tokens: len: 319  data: [4299, 2028, 1458, 3645, 5835, 5835, 5919, 3975, 4299, 2058, 5648, 2313, 4448, 5166, 5175, 5177, 5179, 5418, 4448, 4807, 3590, 6506, 6533, 6532, 5828, 6074, 5750, 4940, 5020, 4776, 5019, 5100, 4299, 5352, 3732, 4299, 6486, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 4218, 5214, 5319, 4752, 127, 5087, 5014, 303, 4484, 6157, 3404, 3412, 6318, 1955, 1220, 575, 5078, 4859, 4616, 2819, 647, 2099, 5415, 5562, 1053, 584, 4319, 5447, 5559, 6315, 5345, 221, 146, 1828, 2064, 6537, 604, 2762, 5693, 4474, 1806, 5651, 4434, 4857, 4525, 3799, 2, 3650, 2924, 2249, 1842, 64, 380, 146, 4509, 4590, 4590, 2342, 4502, 4474, 4473, 5229, 5258, 856, 3799, 50, 566, 1133, 2099, 2105, 6560, 6560, 4373, 1457, 2177, 2084, 2056, 2028, 3645, 5832, 5832, 1788, 4299, 3747, 4394, 5428, 2549, 6435, 6462, 4511, 4403, 2483, 4885, 4992, 5073, 4992, 5344, 4369, 5090, 5509, 5917, 2276, 151, 306, 29, 2264, 1779, 5650, 4435, 2564, 6262, 6258, 4803, 4432, 4432, 4488, 4650, 1295, 1295, 2015, 2015, 2105, 4130, 1943, 1943, 2986, 4833, 4752, 46, 4421, 4477, 4410, 4590, 2314, 4411, 6180, 4420, 2567, 1854, 3471, 5562, 3393, 5824, 4373, 2186, 6534, 6264, 6507, 6507, 2181, 2186, 1457, 1457, 3641, 2000, 1816, 1606, 5333, 6238, 2780, 4319, 692, 146, 4432, 4513, 294, 5021, 6019, 3994, 5250, 3152, 2903, 5048, 4071, 5406, 3348, 2695, 5064, 5046, 6501, 5033, 5186, 731, 857, 3719, 29, 2243, 1923, 60, 2274, 512, 62, 913, 4314, 3833, 5019, 132, 75, 83, 1453, 654, 2835, 5355, 5267, 5111, 77, 3719, 6229, 74, 146, 1370, 1271, 1271, 2109, 2112, 3969, 3645, 5835, 5838, 3732, 1950, 5161, 5405, 2220, 4460, 4946, 713, 4124, 1685, 686, 2861, 6419, 4018, 4015, 3641, 5831, 2833, 5028, 2769, 5057, 6400, 6238, 5995, 6026, 3866, 1679, 1197, 2163, 191, 3650, 2200, 3751, 6379, 2259, 4379, 2192, 776, 1720, 6453, 6535, 685, 6323, 2684, 1229, 2032, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 4299, 3975]\n", "2025-03-02 17:32:50,489 INFO llm job done, time cost: 3.984s\n", "2025-03-02 17:32:50,490 DEBUG speech_tokens: len: 322  data: [6486, 6486, 6486, 6486, 6405, 6405, 6405, 6405, 6405, 4218, 2031, 5651, 4671, 4600, 4438, 5482, 5644, 5176, 5176, 5907, 5260, 4565, 5048, 6506, 6505, 5827, 4447, 4753, 6130, 6373, 6373, 5418, 5419, 1775, 1532, 1759, 6486, 6486, 6486, 6486, 6405, 6405, 4218, 3891, 4482, 4834, 2322, 2894, 5088, 303, 4483, 6400, 2675, 6075, 4132, 1304, 710, 5582, 4615, 2666, 635, 318, 4836, 1377, 593, 5771, 6289, 6317, 143, 1001, 2088, 6453, 3404, 4879, 6012, 2013, 5405, 4506, 2344, 3962, 731, 1466, 138, 63, 2219, 4428, 4510, 2241, 4520, 5150, 5881, 5988, 4531, 3798, 79, 566, 1861, 2012, 2158, 5803, 6532, 4373, 2186, 4299, 6486, 6486, 6486, 6486, 6405, 6405, 6405, 4137, 5835, 5835, 5835, 5835, 1461, 2040, 3744, 5105, 5834, 2079, 6453, 2549, 44, 2699, 5614, 5613, 5640, 6316, 5828, 2864, 3002, 151, 73, 2258, 1122, 3462, 4678, 4939, 5533, 5529, 4722, 5242, 4676, 4677, 4211, 1295, 1295, 1943, 6074, 6317, 3482, 5173, 5320, 4509, 4420, 5122, 5125, 4509, 4509, 4492, 5856, 2233, 803, 1986, 5565, 6294, 5823, 6557, 4373, 6381, 6535, 6534, 4287, 2186, 1457, 1457, 5804, 3498, 6402, 4218, 6486, 6486, 6405, 6405, 6405, 6405, 6405, 6405, 4218, 1959, 3720, 4442, 6157, 4958, 4319, 4373, 1457, 626, 137, 4432, 4432, 2301, 5453, 6259, 1293, 5247, 713, 5074, 6504, 4920, 5562, 3564, 2860, 5776, 5532, 6498, 6158, 1463, 1504, 1774, 2, 66, 2166, 2217, 4623, 538, 116, 1881, 4318, 3829, 5046, 5020, 789, 56, 1117, 654, 651, 4944, 6327, 5995, 2276, 305, 3799, 6066, 2260, 2567, 317, 1280, 1729, 6486, 6486, 6486, 6486, 6486, 6405, 6405, 6405, 6405, 6405, 6405, 6405, 6405, 6405, 6405, 6405, 6405, 4218, 2031, 5136, 4593, 4596, 2310, 5509, 2870, 641, 1612, 1928, 674, 5771, 3749, 1616, 2180, 3644, 5028, 2841, 2850, 5057, 6484, 6481, 6239, 1625, 1679, 3134, 2175, 712, 2918, 5108, 2209, 6180, 4200, 5409, 4538, 734, 767, 3988, 6534, 6534, 2872, 6400, 3413, 1235, 1966, 4227, 6486, 6486, 6486, 6405, 6405, 6405, 4218]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 0 进度：7\n", "任务 4 进度：7\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:50,819 INFO yield speech index:7, len 0.72, rtf 1.025,  cost 0.738s,  all cost time 4.358s\n", "\n", "100%|██████████| 1/1 [00:04<00:00,  4.36s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 2 进度：8\n", "任务 2 完成，生成 8 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:51,317 INFO yield speech index:7, len 3.44, rtf 0.542,  cost 1.864s,  all cost time 4.838s\n", "\n", "\n", "100%|██████████| 1/1 [00:04<00:00,  4.84s/it]\n", "2025-03-02 17:32:51,387 INFO yield speech index:7, len 1.08, rtf 0.969,  cost 1.046s,  all cost time 4.964s\n", "100%|██████████| 1/1 [00:04<00:00,  4.97s/it]\n", "2025-03-02 17:32:51,478 INFO yield speech index:7, len 2.24, rtf 0.507,  cost 1.135s,  all cost time 4.978s\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:04<00:00,  4.98s/it]\n", "2025-03-02 17:32:51,487 INFO yield speech index:7, len 2.72, rtf 0.564,  cost 1.535s,  all cost time 5.043s\n", "100%|██████████| 1/1 [00:05<00:00,  5.05s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 3 进度：8\n", "任务 3 完成，生成 8 个片段\n", "任务 0 进度：8\n", "任务 0 完成，生成 8 个片段\n", "任务 4 进度：8\n", "任务 4 完成，生成 8 个片段\n", "任务 1 进度：8\n", "任务 1 完成，生成 8 个片段\n", "--- 5.091320991516113 seconds ---\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["start_time = time.time()\n", "await test_concurrent_instruct(5, semaphore_limit=5, stream=True)\n", "print(\"--- %s seconds ---\" % (time.time() - start_time))"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/1 [00:00<?, ?it/s]2025-03-02 17:32:51,510 INFO synthesis text 这是任务零，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:51,522 INFO synthesis text 这是任务一，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\u001b[A2025-03-02 17:32:51,541 INFO synthesis text 这是任务二，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\u001b[A\u001b[A2025-03-02 17:32:51,574 INFO synthesis text 这是任务三，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A2025-03-02 17:32:51,593 INFO synthesis text 这是任务四，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:51,605 INFO synthesis text 这是任务五，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:51,615 INFO synthesis text 这是任务六，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:51,623 INFO synthesis text 这是任务七，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:51,634 INFO synthesis text 这是任务八，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A\u001b[A2025-03-02 17:32:51,649 INFO synthesis text 这是任务九，收到好友从远方寄来的生日礼物，那份意外的惊喜与深深的祝福让我心中充满了甜蜜的快乐，笑容如花儿般绽放。\n", "2025-03-02 17:32:52,246 INFO yield speech index:0, len 0.44, rtf 1.415,  cost 0.623s,  all cost time 0.623s\n", "2025-03-02 17:32:52,247 DEBUG multiples: 0.05, peer_chunk_token_num: 15\n", "2025-03-02 17:32:52,284 INFO yield speech index:0, len 0.44, rtf 1.731,  cost 0.762s,  all cost time 0.762s\n", "2025-03-02 17:32:52,285 DEBUG multiples: -0.05, peer_chunk_token_num: 15\n", "2025-03-02 17:32:52,287 INFO yield speech index:0, len 0.44, rtf 1.576,  cost 0.694s,  all cost time 0.694s\n", "2025-03-02 17:32:52,287 DEBUG multiples: -0.05, peer_chunk_token_num: 15\n", "2025-03-02 17:32:52,300 INFO yield speech index:0, len 0.44, rtf 1.795,  cost 0.790s,  all cost time 0.790s\n", "2025-03-02 17:32:52,301 DEBUG multiples: -0.07, peer_chunk_token_num: 15\n", "2025-03-02 17:32:52,341 INFO yield speech index:0, len 0.44, rtf 1.607,  cost 0.707s,  all cost time 0.707s\n", "2025-03-02 17:32:52,342 DEBUG multiples: -0.09, peer_chunk_token_num: 15\n", "2025-03-02 17:32:52,346 INFO yield speech index:0, len 0.44, rtf 1.584,  cost 0.697s,  all cost time 0.697s\n", "2025-03-02 17:32:52,347 DEBUG multiples: -0.07, peer_chunk_token_num: 15\n", "2025-03-02 17:32:52,379 INFO yield speech index:0, len 0.44, rtf 1.759,  cost 0.774s,  all cost time 0.774s\n", "2025-03-02 17:32:52,380 DEBUG multiples: -0.17, peer_chunk_token_num: 15\n", "2025-03-02 17:32:52,394 INFO yield speech index:0, len 0.44, rtf 1.864,  cost 0.820s,  all cost time 0.820s\n", "2025-03-02 17:32:52,394 DEBUG multiples: -0.19, peer_chunk_token_num: 15\n", "2025-03-02 17:32:52,405 INFO yield speech index:0, len 0.44, rtf 1.963,  cost 0.864s,  all cost time 0.864s\n", "2025-03-02 17:32:52,406 DEBUG multiples: -0.20, peer_chunk_token_num: 15\n", "2025-03-02 17:32:52,407 INFO yield speech index:0, len 0.44, rtf 1.800,  cost 0.792s,  all cost time 0.792s\n", "2025-03-02 17:32:52,407 DEBUG multiples: -0.19, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：1\n", "任务 1 进度：1\n", "任务 4 进度：1\n", "任务 0 进度：1\n", "任务 8 进度：1\n", "任务 9 进度：1\n", "任务 5 进度：1\n", "任务 3 进度：1\n", "任务 2 进度：1\n", "任务 6 进度：1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:52,790 INFO yield speech index:1, len 0.60, rtf 0.905,  cost 0.543s,  all cost time 1.166s\n", "2025-03-02 17:32:52,790 DEBUG multiples: 0.15, peer_chunk_token_num: 15\n", "2025-03-02 17:32:52,817 INFO yield speech index:1, len 0.60, rtf 0.888,  cost 0.533s,  all cost time 1.295s\n", "2025-03-02 17:32:52,818 DEBUG multiples: 0.07, peer_chunk_token_num: 15\n", "2025-03-02 17:32:52,852 INFO yield speech index:1, len 0.60, rtf 0.941,  cost 0.565s,  all cost time 1.259s\n", "2025-03-02 17:32:52,852 DEBUG multiples: 0.01, peer_chunk_token_num: 15\n", "2025-03-02 17:32:52,853 INFO yield speech index:1, len 0.60, rtf 0.920,  cost 0.552s,  all cost time 1.343s\n", "2025-03-02 17:32:52,854 DEBUG multiples: 0.00, peer_chunk_token_num: 15\n", "2025-03-02 17:32:52,912 INFO yield speech index:1, len 0.60, rtf 0.941,  cost 0.565s,  all cost time 1.262s\n", "2025-03-02 17:32:52,912 DEBUG multiples: -0.02, peer_chunk_token_num: 15\n", "2025-03-02 17:32:52,949 INFO yield speech index:1, len 0.60, rtf 1.012,  cost 0.607s,  all cost time 1.315s\n", "2025-03-02 17:32:52,950 DEBUG multiples: -0.10, peer_chunk_token_num: 15\n", "2025-03-02 17:32:52,973 INFO yield speech index:1, len 0.60, rtf 0.988,  cost 0.593s,  all cost time 1.368s\n", "2025-03-02 17:32:52,974 DEBUG multiples: -0.18, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：2\n", "任务 1 进度：2\n", "任务 4 进度：2\n", "任务 0 进度：2\n", "任务 9 进度：2\n", "任务 8 进度：2\n", "任务 5 进度：2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:53,015 INFO yield speech index:1, len 0.60, rtf 1.034,  cost 0.620s,  all cost time 1.441s\n", "2025-03-02 17:32:53,015 DEBUG multiples: -0.23, peer_chunk_token_num: 15\n", "2025-03-02 17:32:53,035 INFO yield speech index:1, len 0.60, rtf 1.047,  cost 0.628s,  all cost time 1.420s\n", "2025-03-02 17:32:53,036 DEBUG multiples: -0.25, peer_chunk_token_num: 15\n", "2025-03-02 17:32:53,037 INFO yield speech index:1, len 0.60, rtf 1.052,  cost 0.631s,  all cost time 1.495s\n", "2025-03-02 17:32:53,037 DEBUG multiples: -0.26, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 3 进度：2\n", "任务 6 进度：2\n", "任务 2 进度：2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:53,462 INFO yield speech index:2, len 0.60, rtf 1.016,  cost 0.610s,  all cost time 1.869s\n", "2025-03-02 17:32:53,463 DEBUG multiples: -0.01, peer_chunk_token_num: 15\n", "2025-03-02 17:32:53,465 INFO yield speech index:2, len 0.60, rtf 1.124,  cost 0.674s,  all cost time 1.841s\n", "2025-03-02 17:32:53,465 DEBUG multiples: 0.02, peer_chunk_token_num: 15\n", "2025-03-02 17:32:53,496 INFO yield speech index:2, len 0.60, rtf 1.130,  cost 0.678s,  all cost time 1.974s\n", "2025-03-02 17:32:53,497 DEBUG multiples: -0.07, peer_chunk_token_num: 15\n", "2025-03-02 17:32:53,560 INFO yield speech index:2, len 0.60, rtf 1.079,  cost 0.647s,  all cost time 1.910s\n", "2025-03-02 17:32:53,560 DEBUG multiples: -0.10, peer_chunk_token_num: 15\n", "2025-03-02 17:32:53,590 INFO yield speech index:2, len 0.60, rtf 1.226,  cost 0.736s,  all cost time 2.079s\n", "2025-03-02 17:32:53,590 DEBUG multiples: -0.21, peer_chunk_token_num: 15\n", "2025-03-02 17:32:53,633 INFO yield speech index:2, len 0.60, rtf 1.139,  cost 0.683s,  all cost time 1.999s\n", "2025-03-02 17:32:53,634 DEBUG multiples: -0.23, peer_chunk_token_num: 15\n", "2025-03-02 17:32:53,647 INFO yield speech index:2, len 0.60, rtf 1.122,  cost 0.673s,  all cost time 2.041s\n", "2025-03-02 17:32:53,647 DEBUG multiples: -0.29, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 4 进度：3\n", "任务 7 进度：3\n", "任务 1 进度：3\n", "任务 9 进度：3\n", "任务 0 进度：3\n", "任务 8 进度：3\n", "任务 5 进度：3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:53,688 INFO yield speech index:2, len 0.60, rtf 1.122,  cost 0.673s,  all cost time 2.115s\n", "2025-03-02 17:32:53,689 DEBUG multiples: -0.34, peer_chunk_token_num: 15\n", "2025-03-02 17:32:53,693 INFO yield speech index:2, len 0.60, rtf 1.095,  cost 0.657s,  all cost time 2.078s\n", "2025-03-02 17:32:53,693 DEBUG multiples: -0.34, peer_chunk_token_num: 15\n", "2025-03-02 17:32:53,724 INFO yield speech index:2, len 0.60, rtf 1.145,  cost 0.687s,  all cost time 2.182s\n", "2025-03-02 17:32:53,724 DEBUG multiples: -0.39, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 3 进度：3\n", "任务 6 进度：3\n", "任务 2 进度：3\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:54,204 INFO yield speech index:3, len 0.60, rtf 1.235,  cost 0.741s,  all cost time 2.611s\n", "2025-03-02 17:32:54,204 DEBUG multiples: -0.23, peer_chunk_token_num: 15\n", "2025-03-02 17:32:54,212 INFO yield speech index:3, len 0.60, rtf 1.245,  cost 0.747s,  all cost time 2.589s\n", "2025-03-02 17:32:54,213 DEBUG multiples: -0.22, peer_chunk_token_num: 15\n", "2025-03-02 17:32:54,251 INFO yield speech index:3, len 0.60, rtf 1.257,  cost 0.754s,  all cost time 2.729s\n", "2025-03-02 17:32:54,252 DEBUG multiples: -0.30, peer_chunk_token_num: 15\n", "2025-03-02 17:32:54,288 INFO yield speech index:3, len 0.60, rtf 1.213,  cost 0.728s,  all cost time 2.639s\n", "2025-03-02 17:32:54,289 DEBUG multiples: -0.29, peer_chunk_token_num: 15\n", "2025-03-02 17:32:54,324 INFO yield speech index:3, len 0.60, rtf 1.223,  cost 0.734s,  all cost time 2.814s\n", "2025-03-02 17:32:54,325 DEBUG multiples: -0.40, peer_chunk_token_num: 15\n", "2025-03-02 17:32:54,364 INFO yield speech index:3, len 0.60, rtf 1.217,  cost 0.730s,  all cost time 2.730s\n", "2025-03-02 17:32:54,365 DEBUG multiples: -0.42, peer_chunk_token_num: 15\n", "2025-03-02 17:32:54,373 INFO yield speech index:3, len 0.60, rtf 1.209,  cost 0.726s,  all cost time 2.768s\n", "2025-03-02 17:32:54,374 DEBUG multiples: -0.47, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 4 进度：4\n", "任务 7 进度：4\n", "任务 1 进度：4\n", "任务 9 进度：4\n", "任务 0 进度：4\n", "任务 8 进度：4\n", "任务 5 进度：4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:54,408 INFO yield speech index:3, len 0.60, rtf 1.198,  cost 0.719s,  all cost time 2.834s\n", "2025-03-02 17:32:54,409 DEBUG multiples: -0.51, peer_chunk_token_num: 15\n", "2025-03-02 17:32:54,447 INFO yield speech index:3, len 0.60, rtf 1.257,  cost 0.754s,  all cost time 2.832s\n", "2025-03-02 17:32:54,448 DEBUG multiples: -0.55, peer_chunk_token_num: 15\n", "2025-03-02 17:32:54,482 INFO yield speech index:3, len 0.60, rtf 1.262,  cost 0.757s,  all cost time 2.940s\n", "2025-03-02 17:32:54,483 DEBUG multiples: -0.60, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 3 进度：4\n", "任务 6 进度：4\n", "任务 2 进度：4\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:55,033 INFO yield speech index:4, len 0.60, rtf 1.381,  cost 0.829s,  all cost time 3.440s\n", "2025-03-02 17:32:55,034 DEBUG multiples: -0.56, peer_chunk_token_num: 15\n", "2025-03-02 17:32:55,035 INFO llm job done, time cost: 3.379s\n", "2025-03-02 17:32:55,035 DEBUG speech_tokens: len: 256  data: [5832, 5832, 5832, 3645, 1896, 4677, 4527, 4447, 4914, 4689, 2990, 5898, 5256, 4565, 2861, 6506, 5829, 5824, 1698, 5001, 969, 1352, 2084, 1522, 5562, 2322, 3638, 5094, 1032, 4564, 6400, 2675, 5589, 6318, 1304, 2891, 5342, 5582, 719, 1855, 3219, 4077, 512, 3587, 6205, 6312, 233, 842, 2044, 2148, 1440, 5675, 5609, 6178, 2148, 5405, 4659, 4857, 5906, 734, 1466, 151, 1026, 59, 4437, 4996, 2259, 4511, 5206, 5880, 5988, 5338, 3798, 804, 647, 2014, 2093, 6559, 6559, 2186, 2087, 3402, 5832, 5832, 5832, 1785, 4299, 4393, 5509, 3520, 6534, 4817, 4499, 3427, 1979, 4992, 5100, 5344, 3643, 5090, 5995, 4382, 804, 65, 160, 2004, 4675, 2510, 5048, 4803, 4416, 4591, 4513, 321, 2105, 2096, 1825, 3887, 1943, 2105, 4431, 4590, 4590, 4752, 4439, 4474, 5124, 4752, 4753, 4528, 4392, 6180, 2259, 389, 882, 1032, 6291, 6553, 5831, 3562, 6534, 6537, 2185, 2186, 2186, 2084, 1606, 3886, 5995, 4955, 4319, 728, 218, 4444, 4594, 2274, 4993, 5938, 1041, 3141, 5087, 5776, 4071, 5241, 3321, 4956, 5776, 6504, 2854, 5915, 2921, 1478, 1775, 2, 156, 2166, 2220, 4947, 268, 35, 850, 2130, 4315, 5533, 4857, 795, 59, 389, 2139, 651, 3567, 6327, 5186, 4463, 143, 5986, 3961, 2243, 632, 1999, 5832, 5832, 5835, 3648, 2922, 1869, 3651, 4485, 4755, 4759, 2337, 5266, 5756, 707, 1937, 1694, 713, 3506, 6422, 3934, 6219, 1856, 5828, 2886, 5028, 5028, 2850, 5030, 6401, 6239, 5996, 6050, 3863, 1198, 2148, 1440, 2999, 4382, 2206, 5937, 2013, 5644, 4460, 3647, 1882, 2127, 4272, 3384, 4214, 5600, 1238, 1957, 4299, 4299, 4299, 4218]\n", "2025-03-02 17:32:55,071 INFO yield speech index:4, len 0.60, rtf 1.430,  cost 0.858s,  all cost time 3.448s\n", "2025-03-02 17:32:55,072 DEBUG multiples: -0.58, peer_chunk_token_num: 15\n", "2025-03-02 17:32:55,096 INFO yield speech index:4, len 0.60, rtf 1.407,  cost 0.844s,  all cost time 3.574s\n", "2025-03-02 17:32:55,097 DEBUG multiples: -0.64, peer_chunk_token_num: 15\n", "2025-03-02 17:32:55,130 INFO yield speech index:4, len 0.60, rtf 1.402,  cost 0.841s,  all cost time 3.480s\n", "2025-03-02 17:32:55,130 DEBUG multiples: -0.63, peer_chunk_token_num: 15\n", "2025-03-02 17:32:55,154 INFO yield speech index:4, len 0.60, rtf 1.382,  cost 0.829s,  all cost time 3.643s\n", "2025-03-02 17:32:55,154 DEBUG multiples: -0.71, peer_chunk_token_num: 15\n", "2025-03-02 17:32:55,189 INFO yield speech index:4, len 0.60, rtf 1.373,  cost 0.824s,  all cost time 3.555s\n", "2025-03-02 17:32:55,190 DEBUG multiples: -0.72, peer_chunk_token_num: 15\n", "2025-03-02 17:32:55,212 INFO yield speech index:4, len 0.60, rtf 1.398,  cost 0.839s,  all cost time 3.607s\n", "2025-03-02 17:32:55,213 DEBUG multiples: -0.78, peer_chunk_token_num: 15\n", "2025-03-02 17:32:55,220 INFO llm job done, time cost: 3.567s\n", "2025-03-02 17:32:55,220 DEBUG speech_tokens: len: 268  data: [2916, 5835, 5862, 3648, 2112, 4921, 2331, 4600, 5175, 5653, 5905, 6148, 5418, 4591, 4807, 5072, 4319, 2132, 1403, 2879, 1979, 1949, 6482, 6238, 3836, 1688, 5238, 5319, 3368, 5099, 2664, 306, 4537, 4700, 680, 5589, 6328, 575, 707, 4613, 4610, 725, 725, 309, 5562, 1305, 719, 4319, 5530, 6315, 3401, 62, 758, 2071, 6537, 1440, 3488, 4964, 4716, 2130, 4922, 4434, 4533, 5986, 734, 3653, 8, 315, 2252, 4428, 4590, 2331, 4520, 4474, 5206, 5314, 5341, 5261, 1613, 1533, 404, 1376, 2095, 2099, 4372, 6531, 6533, 4373, 3644, 2084, 1701, 5835, 5943, 5862, 5835, 1461, 2121, 5850, 5186, 6157, 4356, 6534, 2657, 4526, 5614, 5613, 5019, 5338, 3643, 5078, 5188, 2192, 1044, 59, 807, 2004, 4432, 2508, 5047, 6261, 5529, 4417, 4594, 4512, 3237, 3482, 2753, 4292, 1700, 1943, 1862, 3715, 4593, 4509, 4439, 4396, 5206, 4509, 2250, 4491, 5934, 2475, 2243, 802, 1275, 5565, 6309, 5824, 4373, 4288, 6534, 6537, 2182, 1454, 1457, 1454, 3152, 3886, 6065, 5995, 4874, 1403, 4373, 1457, 1436, 788, 4590, 4569, 2536, 4723, 6261, 5172, 3882, 5096, 5047, 6255, 2733, 5562, 3411, 5046, 5043, 6501, 6498, 5834, 1460, 11, 3935, 290, 2, 876, 303, 4461, 2698, 278, 904, 2127, 4068, 5073, 4776, 801, 2246, 1370, 654, 651, 5598, 5267, 2921, 59, 3962, 6229, 2504, 4772, 632, 1757, 1458, 5835, 5946, 5865, 5838, 5109, 1059, 5133, 4756, 2337, 4540, 4942, 5084, 2912, 4043, 1694, 623, 2861, 5777, 5798, 4042, 4124, 1937, 4373, 5830, 2841, 5028, 2850, 5027, 6238, 6247, 5995, 3812, 1649, 2163, 1431, 2999, 5108, 3666, 1806, 5650, 4437, 4487, 2189, 11, 2044, 6537, 6534, 2899, 5753, 5591, 497, 1229, 2112]\n", "2025-03-02 17:32:55,221 INFO yield speech index:4, len 0.60, rtf 1.355,  cost 0.813s,  all cost time 3.648s\n", "2025-03-02 17:32:55,222 DEBUG multiples: -0.79, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 4 进度：5\n", "任务 7 进度：5\n", "任务 1 进度：5\n", "任务 9 进度：5\n", "任务 0 进度：5\n", "任务 8 进度：5\n", "任务 5 进度：5\n", "任务 3 进度：5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:55,276 INFO yield speech index:4, len 0.60, rtf 1.380,  cost 0.828s,  all cost time 3.661s\n", "2025-03-02 17:32:55,278 DEBUG multiples: -0.85, peer_chunk_token_num: 15\n", "2025-03-02 17:32:55,383 INFO yield speech index:4, len 0.60, rtf 1.499,  cost 0.900s,  all cost time 3.841s\n", "2025-03-02 17:32:55,383 DEBUG multiples: -0.98, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 6 进度：5\n", "任务 2 进度：5\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:55,612 INFO llm job done, time cost: 3.959s\n", "2025-03-02 17:32:55,613 DEBUG speech_tokens: len: 295  data: [2112, 1945, 6075, 6075, 3645, 3645, 3726, 5832, 5832, 3648, 1785, 2112, 5647, 2493, 4763, 4437, 4590, 5175, 5907, 5166, 4523, 4565, 5777, 4346, 4367, 4428, 5645, 3231, 4460, 5837, 4469, 5203, 6177, 4299, 4134, 5832, 3648, 1707, 4752, 4833, 2638, 5086, 303, 4754, 5753, 5589, 5592, 1304, 704, 4858, 4616, 713, 725, 1041, 5562, 3483, 701, 1403, 6422, 6178, 6178, 2059, 2032, 1862, 4129, 3887, 194, 32, 1829, 6534, 6534, 650, 5591, 4963, 6501, 1275, 4676, 4497, 4453, 3718, 5, 2192, 156, 63, 65, 4428, 4753, 2484, 2252, 4394, 5206, 4584, 5260, 2990, 3718, 1534, 647, 2014, 2014, 2077, 6533, 6560, 1457, 2087, 2032, 2031, 4212, 6075, 5832, 3645, 1704, 2112, 3747, 4420, 4538, 6453, 3600, 4592, 4966, 5614, 5020, 5344, 4369, 5090, 5996, 4463, 807, 74, 807, 4191, 4435, 4778, 5074, 4803, 4417, 4510, 4675, 2463, 3482, 2753, 3482, 1376, 1853, 2086, 1700, 4130, 4292, 4444, 4753, 5645, 72, 4520, 5122, 4680, 2250, 4519, 5202, 6093, 56, 1611, 5406, 6291, 6552, 5831, 4291, 6507, 6538, 6537, 2185, 2186, 2105, 2186, 2186, 2087, 4299, 1951, 1525, 5986, 5186, 4943, 2861, 3590, 692, 380, 4431, 4510, 2274, 5021, 6262, 6259, 3237, 5490, 5087, 5057, 5048, 5693, 5689, 4071, 2490, 5319, 3483, 5047, 5772, 6501, 5033, 5837, 11, 1478, 1748, 65, 2085, 33, 2760, 269, 932, 2131, 1888, 4966, 4587, 1041, 2243, 1453, 1383, 651, 5028, 6328, 5995, 3005, 77, 5908, 3961, 4511, 389, 542, 2056, 1947, 3888, 5832, 5835, 5832, 5832, 3648, 1869, 4650, 4753, 4434, 4606, 6481, 5030, 713, 1937, 1937, 1442, 674, 3590, 6422, 1987, 2032, 1855, 4041, 4124, 1457, 2915, 5831, 2769, 4947, 5028, 2878, 6485, 6401, 6238, 5267, 947, 947, 1926, 2160, 407, 4376, 5122, 5856, 3993, 2112, 5651, 4449, 5272, 2189, 2125, 4359, 6534, 2899, 6401, 1226, 1957, 1945, 4299]\n", "2025-03-02 17:32:55,630 INFO llm job done, time cost: 3.976s\n", "2025-03-02 17:32:55,630 DEBUG speech_tokens: len: 296  data: [2031, 2112, 4920, 2331, 4844, 4681, 4753, 3232, 3962, 3960, 5409, 5176, 4511, 2693, 4154, 4235, 6560, 6559, 4373, 1451, 1611, 1530, 4858, 2420, 461, 707, 719, 2139, 4299, 4299, 2112, 5136, 4510, 2322, 2891, 4771, 1032, 4484, 4780, 5672, 2756, 5589, 6327, 1304, 683, 2909, 4616, 4616, 461, 719, 1454, 303, 5562, 1305, 683, 3506, 6179, 6286, 4128, 3158, 110, 776, 1802, 6534, 685, 6404, 4880, 5689, 6258, 3471, 4675, 4749, 4517, 3719, 1463, 8, 156, 306, 137, 4438, 4591, 2484, 2333, 5231, 5203, 5230, 5175, 4527, 5177, 1531, 3799, 76, 1295, 1133, 2093, 4370, 5826, 5828, 2186, 2096, 2112, 4299, 1806, 5850, 5185, 5429, 4266, 6534, 4844, 4415, 5857, 6181, 1978, 1987, 4911, 4776, 5825, 3643, 5050, 5915, 8, 888, 315, 65, 1617, 5647, 2256, 4994, 5696, 5776, 4749, 4513, 4432, 4432, 2499, 3239, 1052, 1043, 2059, 2031, 2095, 6317, 4130, 1943, 1862, 5173, 4590, 4428, 2264, 4421, 5203, 4423, 4752, 4448, 4392, 6180, 2260, 56, 801, 1842, 6295, 4122, 5097, 5828, 2186, 6453, 6534, 6534, 2183, 4370, 2186, 2140, 2028, 6075, 3645, 5832, 5835, 5835, 5835, 5835, 5838, 1464, 1869, 3875, 5995, 5023, 5777, 3590, 626, 2264, 4752, 4756, 2562, 4966, 6261, 3993, 5484, 5582, 5081, 5777, 5772, 1041, 5322, 3483, 4957, 2856, 6504, 5041, 5429, 740, 1505, 3800, 47, 326, 353, 957, 1761, 2355, 501, 62, 1881, 4317, 3833, 5073, 159, 67, 461, 573, 651, 2769, 5680, 5266, 2924, 149, 3719, 5986, 317, 2567, 623, 1271, 2002, 3888, 3645, 3645, 3729, 5835, 5838, 5838, 3651, 2112, 5109, 4515, 4675, 2337, 5509, 5053, 4124, 3880, 1928, 707, 2861, 6422, 6178, 6205, 3720, 1856, 1454, 5102, 4947, 5028, 2887, 5755, 6238, 6238, 3809, 1649, 1190, 2172, 712, 5834, 4412, 5853, 6018, 6380, 2260, 4456, 2189, 1100, 4231, 6534, 6534, 5024, 6320, 506, 1960, 2040]\n", "2025-03-02 17:32:55,659 INFO llm job done, time cost: 4.007s\n", "2025-03-02 17:32:55,659 DEBUG speech_tokens: len: 298  data: [2112, 2112, 1032, 4752, 4772, 5166, 4995, 3962, 3961, 5421, 5175, 4599, 5068, 3506, 2132, 2158, 2186, 1769, 2006, 1285, 4939, 4939, 4913, 5073, 5073, 4299, 4299, 5109, 5919, 5838, 3732, 4299, 4299, 4218, 5214, 5565, 216, 2900, 2827, 1275, 4484, 6158, 488, 5589, 4132, 1952, 683, 3638, 4859, 5587, 461, 719, 2012, 1761, 6048, 1053, 665, 4319, 5447, 5560, 4128, 3158, 143, 788, 1523, 1830, 6535, 6534, 5672, 4951, 5769, 5529, 2121, 6379, 4672, 4776, 4454, 3719, 74, 1463, 2921, 66, 2243, 146, 4428, 4591, 2322, 4520, 4393, 5367, 5394, 4527, 4448, 1477, 1614, 75, 647, 2105, 1843, 4299, 2149, 5830, 5827, 3644, 2186, 2140, 1947, 5832, 5859, 5835, 5838, 5838, 5838, 3732, 2112, 1806, 3666, 4379, 6158, 1333, 6537, 4275, 4511, 2228, 23, 1745, 1979, 3481, 5100, 5019, 2753, 1936, 3643, 5051, 5998, 2222, 1038, 59, 879, 4191, 5405, 2481, 5777, 6259, 4803, 4668, 5483, 5648, 3237, 3482, 566, 2006, 2005, 1942, 1943, 3724, 4590, 4833, 72, 4415, 4476, 5205, 4509, 2331, 4491, 6180, 2233, 65, 1770, 6051, 6309, 5824, 3644, 2102, 6534, 6537, 2186, 2105, 1457, 4373, 6560, 2168, 2112, 2112, 3721, 4526, 5186, 2777, 4319, 719, 887, 4428, 4756, 294, 5696, 6262, 2499, 3135, 2891, 5776, 5772, 1905, 6051, 1377, 4873, 5038, 5695, 5772, 6504, 3583, 5834, 740, 1505, 1775, 2192, 1677, 57, 4623, 539, 278, 113, 1881, 6501, 3832, 5020, 1527, 64, 226, 573, 2838, 4869, 5266, 5921, 278, 808, 6148, 4447, 4511, 308, 2002, 1731, 3648, 5835, 5835, 5835, 5835, 5838, 1545, 5379, 4754, 4677, 4571, 6401, 2903, 1208, 3887, 461, 692, 5777, 5693, 3992, 3801, 2099, 2186, 5101, 2760, 5028, 2886, 5026, 6237, 5995, 3839, 1685, 2169, 2160, 3002, 4387, 5937, 1995, 5648, 4492, 5188, 38, 3989, 6537, 3627, 5509, 4862, 1238, 2041, 2031, 2112, 1704, 5832, 5832, 5835, 5835, 3645]\n", "2025-03-02 17:32:55,750 INFO llm job done, time cost: 4.097s\n", "2025-03-02 17:32:55,751 DEBUG speech_tokens: len: 304  data: [2031, 3402, 3645, 5835, 5835, 5835, 5835, 3732, 4299, 2112, 5648, 2232, 4600, 5401, 6455, 6390, 5176, 5905, 3963, 4448, 4540, 5044, 5777, 5774, 5801, 3644, 3641, 2159, 2156, 2186, 2177, 2167, 2087, 2011, 5373, 5726, 4915, 2504, 4997, 225, 55, 5266, 5429, 3403, 5589, 4141, 1952, 575, 707, 5344, 5587, 2900, 725, 1126, 4920, 1134, 593, 674, 3989, 6316, 3158, 113, 119, 1801, 2148, 6534, 2759, 4874, 4636, 6018, 5649, 4432, 4911, 2419, 803, 1463, 8, 957, 64, 56, 5401, 5726, 306, 2252, 4393, 5121, 5364, 5472, 4447, 2252, 5987, 1611, 80, 566, 1133, 2093, 2185, 6556, 5827, 5828, 1457, 2177, 2059, 1216, 3888, 5832, 5835, 5835, 5835, 5832, 1542, 4299, 2130, 4393, 5266, 2548, 6507, 3609, 4568, 4670, 4885, 4911, 5019, 4857, 4285, 3616, 5050, 5266, 4490, 804, 32, 59, 2013, 4675, 2482, 4967, 5777, 4803, 4404, 4513, 4731, 3239, 323, 404, 1043, 2095, 4130, 1943, 1538, 4512, 5402, 2484, 4439, 4420, 5205, 5412, 5401, 4448, 4420, 5937, 4419, 2486, 802, 1977, 4836, 4086, 6553, 3560, 1373, 6426, 6507, 6537, 2182, 2186, 2102, 1454, 1457, 2090, 1762, 1525, 4448, 6157, 4954, 6506, 2888, 707, 71, 4509, 4513, 4515, 2834, 6262, 6262, 1050, 2334, 3395, 2906, 5777, 5769, 1806, 4752, 3321, 2779, 5047, 6501, 6501, 5033, 5834, 776, 1775, 731, 59, 1923, 2220, 4704, 511, 116, 1881, 4315, 3833, 5020, 156, 306, 56, 1443, 654, 3567, 5599, 5996, 5921, 2195, 5909, 5986, 4439, 2333, 1037, 1756, 2112, 3888, 1701, 6075, 5913, 5832, 5832, 5835, 5919, 1788, 2031, 4407, 5486, 2328, 5272, 5751, 5057, 3557, 3965, 3233, 623, 701, 2861, 3590, 6176, 3776, 1856, 1457, 5831, 2796, 4947, 2770, 6481, 6481, 5995, 6022, 3836, 461, 2166, 469, 5837, 2192, 5856, 3993, 6374, 4447, 4460, 2, 1802, 1830, 6540, 4356, 5024, 5600, 1238, 1958, 4299, 4299, 4299, 4299, 1788, 6486, 6486, 4299, 4299, 6162, 4299]\n", "2025-03-02 17:32:55,826 INFO llm job done, time cost: 4.173s\n", "2025-03-02 17:32:55,827 DEBUG speech_tokens: len: 309  data: [4299, 4299, 3975, 5835, 5916, 5835, 5835, 3648, 2112, 1275, 4672, 2234, 2990, 4672, 5401, 6147, 3721, 3721, 5179, 4564, 5045, 4319, 6533, 4372, 4367, 4428, 5402, 5402, 6374, 5418, 5176, 1532, 1759, 2112, 4299, 4299, 4299, 3645, 5832, 5832, 5835, 5835, 5838, 1869, 5379, 4590, 189, 5096, 5094, 300, 4564, 5671, 2674, 5589, 6318, 1301, 1223, 710, 3638, 5829, 4616, 704, 638, 1116, 4998, 1134, 511, 1403, 4319, 5771, 6289, 6317, 971, 869, 1847, 4278, 6534, 569, 5591, 5608, 5526, 2067, 5647, 4443, 4614, 3070, 5, 2195, 876, 63, 2252, 4428, 4753, 2493, 4502, 4717, 5365, 5229, 4500, 2962, 3800, 778, 566, 1286, 1762, 2093, 4373, 6559, 5830, 2186, 2167, 4299, 6486, 4299, 4299, 6078, 5832, 5832, 5832, 5832, 3648, 4299, 2112, 3666, 5185, 2513, 6537, 6543, 2324, 4642, 5614, 4885, 4911, 4858, 6553, 3642, 5051, 5111, 138, 65, 2267, 2085, 4676, 2508, 5776, 5529, 4722, 4509, 4513, 4432, 3237, 1780, 2024, 2015, 2005, 1987, 2104, 1943, 1861, 1862, 5173, 4590, 4753, 2484, 4439, 4395, 5124, 4429, 4428, 4527, 6096, 4420, 74, 2067, 5001, 6294, 6552, 5830, 2186, 6453, 6534, 6534, 2182, 2159, 4373, 2186, 2087, 2112, 4299, 4299, 2040, 1533, 4487, 4943, 2051, 1403, 1433, 4444, 4513, 4488, 3211, 6181, 6262, 2742, 3153, 2891, 5777, 5770, 3471, 4833, 3564, 5047, 5773, 5529, 6501, 5429, 1463, 794, 1775, 2018, 245, 785, 2112, 870, 2274, 4947, 2679, 2753, 62, 32, 1806, 4074, 6018, 5073, 2347, 309, 218, 1410, 654, 2841, 5599, 5995, 4463, 68, 5987, 803, 218, 2000, 4299, 4299, 4299, 4218, 5832, 5832, 5832, 5832, 5835, 1788, 4137, 4405, 4675, 2229, 4540, 4946, 707, 1937, 3881, 1190, 2861, 5774, 3854, 1611, 1937, 6557, 2914, 5028, 5028, 4947, 5027, 5509, 5510, 5996, 1652, 1649, 1197, 2160, 2270, 5105, 2208, 6261, 4191, 6374, 4414, 5914, 254, 1721, 1801, 6456, 6534, 2513, 4862, 1226, 1951, 4299, 4299, 4299, 4299, 4299, 4299]\n", "2025-03-02 17:32:55,854 INFO yield speech index:5, len 0.60, rtf 1.303,  cost 0.782s,  all cost time 4.230s\n", "2025-03-02 17:32:55,854 DEBUG multiples: -0.83, peer_chunk_token_num: 15\n", "2025-03-02 17:32:55,860 INFO llm job done, time cost: 4.205s\n", "2025-03-02 17:32:55,860 DEBUG speech_tokens: len: 311  data: [6075, 5832, 5859, 5859, 5943, 5943, 5943, 5943, 5943, 1788, 4299, 2004, 6374, 4419, 4447, 5401, 6374, 5176, 4448, 2992, 4448, 4483, 5048, 6506, 4319, 2156, 888, 4922, 4432, 4461, 4704, 5020, 2753, 2753, 4831, 3526, 1951, 5838, 5838, 5838, 5835, 5835, 1542, 5379, 5402, 4644, 4853, 5014, 1275, 4492, 5509, 5429, 572, 488, 5589, 5592, 1223, 683, 2900, 5345, 5342, 2909, 710, 2099, 3471, 4752, 495, 674, 2048, 4235, 6260, 6286, 4128, 5345, 113, 1517, 1802, 6537, 658, 3491, 4961, 5446, 6262, 5659, 4440, 4776, 4517, 802, 8, 2924, 157, 1278, 2246, 2252, 5725, 6373, 4412, 4411, 5850, 5148, 4503, 4528, 3799, 79, 647, 1862, 2099, 2185, 4292, 6559, 5831, 2186, 1448, 2109, 6156, 5832, 5832, 5832, 3645, 2112, 4299, 5934, 5850, 4490, 2402, 6210, 4086, 4835, 2219, 2942, 4886, 2724, 5640, 4992, 5345, 2182, 2913, 5024, 4463, 75, 65, 2255, 1770, 6377, 2239, 5534, 5772, 4426, 5483, 4435, 2463, 1295, 1052, 1295, 2015, 2104, 6317, 1943, 1529, 4509, 4509, 2236, 4442, 5126, 4671, 4446, 4500, 3666, 2261, 2990, 3462, 5565, 6309, 5094, 4373, 4292, 6537, 6537, 6534, 2102, 2186, 3641, 3640, 2031, 6075, 5832, 5835, 5835, 5835, 1788, 4299, 3690, 4402, 5671, 674, 1403, 719, 137, 4444, 5402, 2481, 5804, 6421, 4075, 4674, 3314, 5093, 6506, 5776, 3585, 4920, 4833, 2754, 5066, 6506, 5776, 4314, 5429, 2927, 749, 3773, 290, 5, 957, 786, 2274, 2760, 377, 35, 1639, 4315, 4072, 5101, 2346, 75, 140, 1437, 654, 2838, 5598, 5266, 5192, 35, 3722, 3719, 2261, 146, 623, 1001, 2112, 5832, 5835, 5835, 5919, 3732, 4299, 4137, 4432, 4678, 4579, 6481, 2843, 2912, 3881, 1928, 716, 674, 5777, 6422, 4262, 2099, 6560, 2886, 5025, 2850, 5083, 6481, 6157, 6239, 1622, 1649, 1676, 3872, 2175, 469, 3731, 2192, 2209, 6180, 2076, 6374, 4450, 5188, 2, 2126, 4257, 6534, 6534, 5024, 5600, 1226, 1226, 1957, 4215, 4299, 4299, 4299, 6486, 6486, 6486, 4299]\n", "2025-03-02 17:32:55,902 INFO yield speech index:5, len 0.60, rtf 1.446,  cost 0.868s,  all cost time 4.309s\n", "2025-03-02 17:32:55,902 DEBUG multiples: -0.91, peer_chunk_token_num: 15\n", "2025-03-02 17:32:55,925 INFO llm job done, time cost: 4.274s\n", "2025-03-02 17:32:55,925 DEBUG speech_tokens: len: 316  data: [4299, 6486, 6486, 6486, 6486, 2112, 4675, 2493, 5177, 5176, 4752, 4753, 3231, 5176, 5177, 5909, 5169, 4604, 4807, 5048, 4319, 6506, 5828, 3881, 3799, 76, 395, 620, 1079, 3911, 6260, 6261, 4308, 4299, 4218, 5832, 5832, 5835, 5832, 3648, 3648, 4218, 4482, 5725, 2241, 5096, 2907, 306, 4537, 3485, 6318, 4131, 1949, 656, 5096, 5587, 5587, 2657, 707, 1126, 5001, 1161, 503, 3587, 5449, 5560, 224, 761, 767, 6534, 4347, 2759, 4880, 5205, 1806, 5404, 4678, 4776, 2341, 1046, 1466, 58, 73, 2243, 5157, 6374, 2223, 4412, 4393, 5206, 5232, 5257, 2342, 3799, 73, 566, 1375, 1769, 2093, 6559, 6560, 4373, 2186, 2186, 2139, 4299, 4299, 4218, 3750, 1479, 4376, 4780, 6534, 6534, 4592, 4418, 4885, 4911, 4965, 4912, 2591, 3641, 5831, 4808, 4463, 63, 65, 71, 2004, 5161, 2445, 4967, 5532, 4479, 4428, 4432, 4408, 4677, 1294, 2024, 2015, 1285, 2112, 4299, 6486, 4218, 2031, 1700, 4130, 1538, 4429, 6373, 2261, 4448, 4476, 5644, 6373, 4420, 5853, 4393, 2225, 1506, 1248, 5562, 5580, 5094, 2915, 4275, 6534, 6534, 6534, 2186, 2186, 1457, 4373, 4373, 2140, 2031, 3888, 5859, 5835, 5835, 5835, 3651, 1545, 1554, 6044, 5266, 5048, 728, 137, 4432, 4488, 4994, 6263, 6261, 4755, 2584, 5054, 5776, 5772, 1806, 4833, 2754, 5038, 5047, 6501, 6501, 5510, 3647, 11, 3935, 29, 876, 60, 2517, 269, 125, 2127, 4314, 5317, 2427, 66, 380, 2139, 654, 3570, 5599, 5266, 5273, 62, 5905, 3799, 2243, 2495, 308, 1028, 1514, 2109, 4299, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 4299, 3651, 5217, 4753, 2256, 4568, 5752, 2879, 1370, 3721, 1199, 719, 2132, 5690, 3751, 1613, 4373, 2914, 5028, 5028, 5038, 6238, 6158, 5995, 5266, 3836, 1679, 947, 2163, 2166, 434, 5834, 2228, 5853, 1563, 5650, 4689, 4460, 83, 1316, 2044, 6510, 6535, 3600, 5672, 2684, 1238, 1958, 2113, 4299, 6486, 6486, 6486, 6486, 4299]\n", "2025-03-02 17:32:55,959 INFO yield speech index:5, len 0.60, rtf 1.437,  cost 0.862s,  all cost time 4.436s\n", "2025-03-02 17:32:55,960 DEBUG multiples: -0.98, peer_chunk_token_num: 15\n", "2025-03-02 17:32:55,988 INFO llm job done, time cost: 4.333s\n", "2025-03-02 17:32:55,989 DEBUG speech_tokens: len: 320  data: [2112, 4131, 6075, 3645, 6075, 5832, 5832, 5832, 5835, 5835, 3729, 4299, 4299, 5650, 4671, 4501, 5166, 5247, 5256, 5905, 5895, 5986, 4592, 5099, 5804, 5803, 5831, 2186, 2157, 2166, 712, 3971, 6077, 6158, 5429, 5438, 5437, 3888, 2025, 4299, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 4299, 3975, 5214, 5319, 2322, 2828, 5014, 1281, 4457, 6238, 3485, 4860, 5589, 1220, 1952, 2897, 5342, 5587, 4853, 626, 1289, 2733, 3375, 592, 674, 5690, 5587, 6073, 221, 110, 1828, 6537, 4347, 4136, 4880, 4473, 3993, 5405, 4677, 5100, 2428, 3800, 803, 1463, 2921, 138, 2251, 2243, 4428, 4833, 2331, 4511, 5230, 6043, 6070, 6058, 3799, 1611, 404, 1295, 1862, 2092, 2105, 3644, 5830, 2915, 1457, 1448, 2059, 2112, 6486, 6486, 2031, 6015, 2206, 5266, 2548, 6534, 6453, 4763, 4430, 4415, 4643, 2699, 1250, 2031, 1950, 3454, 5100, 5100, 3887, 3643, 5078, 5266, 4463, 801, 74, 68, 1851, 4678, 4677, 4994, 6263, 6182, 4750, 4674, 4676, 4678, 3480, 566, 1285, 2006, 2112, 4299, 1978, 2104, 4129, 1943, 3725, 5319, 4509, 2252, 4423, 5209, 4755, 2313, 4430, 5202, 5364, 2252, 1587, 3948, 3861, 5824, 5831, 4279, 6507, 6507, 6507, 4359, 2186, 1457, 1448, 2000, 2112, 1572, 1696, 5996, 4942, 2861, 689, 797, 4512, 4516, 2562, 5453, 6259, 3237, 3141, 2903, 5777, 5769, 1032, 5319, 3483, 4967, 5047, 6501, 5770, 6157, 1469, 1586, 1019, 5, 948, 30, 4704, 510, 89, 1642, 4315, 5534, 5020, 1842, 2243, 2584, 654, 2838, 2682, 5194, 4463, 158, 3962, 3962, 2243, 308, 2000, 2112, 1944, 3888, 3888, 3645, 5832, 5835, 5835, 5916, 5919, 5919, 5838, 3732, 3975, 4299, 4299, 4299, 3894, 4512, 4513, 4758, 4588, 5509, 5053, 2903, 3881, 3881, 1919, 611, 1403, 4319, 5801, 4043, 1856, 3641, 2887, 5028, 4947, 5602, 5752, 5671, 5996, 3812, 3839, 1197, 2166, 677, 3647, 4460, 5934, 1806, 5648, 4450, 5188, 11, 2126, 1827, 6534, 6534, 2837, 3407, 1229, 1952, 1951, 1945, 4299, 4299, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 6486, 4299]\n", "2025-03-02 17:32:56,049 INFO yield speech index:5, len 0.60, rtf 1.530,  cost 0.918s,  all cost time 4.399s\n", "2025-03-02 17:32:56,051 DEBUG multiples: -1.03, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：6\n", "任务 4 进度：6\n", "任务 1 进度：6\n", "任务 9 进度：6\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:56,085 INFO yield speech index:5, len 0.60, rtf 1.551,  cost 0.930s,  all cost time 4.574s\n", "2025-03-02 17:32:56,088 DEBUG multiples: -1.13, peer_chunk_token_num: 15\n", "2025-03-02 17:32:56,106 INFO yield speech index:5, len 0.60, rtf 1.528,  cost 0.917s,  all cost time 4.472s\n", "2025-03-02 17:32:56,109 DEBUG multiples: -1.12, peer_chunk_token_num: 15\n", "2025-03-02 17:32:56,170 INFO yield speech index:5, len 0.60, rtf 1.595,  cost 0.957s,  all cost time 4.565s\n", "2025-03-02 17:32:56,173 DEBUG multiples: -1.22, peer_chunk_token_num: 15\n", "2025-03-02 17:32:56,230 INFO yield speech index:5, len 0.60, rtf 1.680,  cost 1.008s,  all cost time 4.656s\n", "2025-03-02 17:32:56,232 DEBUG multiples: -1.28, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 0 进度：6\n", "任务 8 进度：6\n", "任务 5 进度：6\n", "任务 3 进度：6\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:56,344 INFO yield speech index:5, len 0.60, rtf 1.777,  cost 1.066s,  all cost time 4.729s\n", "2025-03-02 17:32:56,347 DEBUG multiples: -1.39, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 6 进度：6\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:56,586 INFO yield speech index:5, len 0.60, rtf 2.004,  cost 1.202s,  all cost time 5.044s\n", "2025-03-02 17:32:56,588 DEBUG multiples: -1.62, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 2 进度：6\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:56,939 INFO yield speech index:6, len 0.60, rtf 1.808,  cost 1.085s,  all cost time 5.316s\n", "2025-03-02 17:32:56,943 DEBUG multiples: -1.42, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：7\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:57,207 INFO yield speech index:6, len 0.60, rtf 2.174,  cost 1.304s,  all cost time 5.613s\n", "2025-03-02 17:32:57,207 DEBUG multiples: -1.70, peer_chunk_token_num: 15\n", "2025-03-02 17:32:57,228 INFO yield speech index:6, len 0.60, rtf 2.114,  cost 1.268s,  all cost time 5.706s\n", "2025-03-02 17:32:57,228 DEBUG multiples: -1.72, peer_chunk_token_num: 15\n", "2025-03-02 17:32:57,292 INFO yield speech index:6, len 0.60, rtf 2.069,  cost 1.241s,  all cost time 5.643s\n", "2025-03-02 17:32:57,295 DEBUG multiples: -1.74, peer_chunk_token_num: 15\n", "2025-03-02 17:32:57,367 INFO yield speech index:6, len 0.60, rtf 1.991,  cost 1.195s,  all cost time 5.762s\n", "2025-03-02 17:32:57,370 DEBUG multiples: -1.85, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 4 进度：7\n", "任务 1 进度：7\n", "任务 9 进度：7\n", "任务 5 进度：7\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:57,606 INFO yield speech index:6, len 0.60, rtf 2.290,  cost 1.374s,  all cost time 6.032s\n", "2025-03-02 17:32:57,609 DEBUG multiples: -2.06, peer_chunk_token_num: 15\n", "2025-03-02 17:32:57,611 INFO yield speech index:6, len 0.60, rtf 2.539,  cost 1.524s,  all cost time 6.101s\n", "2025-03-02 17:32:57,614 DEBUG multiples: -2.07, peer_chunk_token_num: 15\n", "2025-03-02 17:32:57,648 INFO yield speech index:6, len 0.60, rtf 2.564,  cost 1.539s,  all cost time 6.013s\n", "2025-03-02 17:32:57,649 DEBUG multiples: -2.07, peer_chunk_token_num: 15\n", "2025-03-02 17:32:57,682 INFO yield speech index:6, len 0.60, rtf 2.225,  cost 1.335s,  all cost time 6.067s\n", "2025-03-02 17:32:57,682 DEBUG multiples: -2.11, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 3 进度：7\n", "任务 0 进度：7\n", "任务 8 进度：7\n", "任务 6 进度：7\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:57,860 INFO yield speech index:6, len 0.60, rtf 2.120,  cost 1.272s,  all cost time 6.318s\n", "2025-03-02 17:32:57,862 DEBUG multiples: -2.26, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 2 进度：7\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:58,295 INFO yield speech index:7, len 0.60, rtf 2.254,  cost 1.352s,  all cost time 6.671s\n", "2025-03-02 17:32:58,297 DEBUG multiples: -2.20, peer_chunk_token_num: 15\n", "2025-03-02 17:32:58,462 INFO yield speech index:7, len 0.60, rtf 2.092,  cost 1.255s,  all cost time 6.869s\n", "2025-03-02 17:32:58,465 DEBUG multiples: -2.36, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：8\n", "任务 4 进度：8\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:58,630 INFO yield speech index:7, len 0.60, rtf 2.336,  cost 1.401s,  all cost time 7.108s\n", "2025-03-02 17:32:58,633 DEBUG multiples: -2.50, peer_chunk_token_num: 15\n", "2025-03-02 17:32:58,653 INFO yield speech index:7, len 0.60, rtf 2.264,  cost 1.358s,  all cost time 7.004s\n", "2025-03-02 17:32:58,656 DEBUG multiples: -2.48, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 1 进度：8\n", "任务 9 进度：8\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:58,844 INFO yield speech index:7, len 0.60, rtf 2.457,  cost 1.474s,  all cost time 7.238s\n", "2025-03-02 17:32:58,846 DEBUG multiples: -2.66, peer_chunk_token_num: 15\n", "2025-03-02 17:32:58,892 INFO yield speech index:7, len 0.60, rtf 2.131,  cost 1.278s,  all cost time 7.381s\n", "2025-03-02 17:32:58,895 DEBUG multiples: -2.70, peer_chunk_token_num: 15\n", "2025-03-02 17:32:58,998 INFO yield speech index:7, len 0.60, rtf 2.315,  cost 1.389s,  all cost time 7.424s\n", "2025-03-02 17:32:59,000 DEBUG multiples: -2.77, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 5 进度：8\n", "任务 0 进度：8\n", "任务 3 进度：8\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:59,114 INFO yield speech index:7, len 0.60, rtf 2.442,  cost 1.465s,  all cost time 7.480s\n", "2025-03-02 17:32:59,115 DEBUG multiples: -2.83, peer_chunk_token_num: 15\n", "2025-03-02 17:32:59,119 INFO yield speech index:7, len 0.60, rtf 2.394,  cost 1.436s,  all cost time 7.504s\n", "2025-03-02 17:32:59,119 DEBUG multiples: -2.85, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 8 进度：8\n", "任务 6 进度：8\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:59,379 INFO yield speech index:7, len 0.60, rtf 2.528,  cost 1.517s,  all cost time 7.837s\n", "2025-03-02 17:32:59,381 DEBUG multiples: -3.03, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 2 进度：8\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:32:59,806 INFO yield speech index:8, len 0.60, rtf 2.515,  cost 1.509s,  all cost time 8.183s\n", "2025-03-02 17:32:59,809 DEBUG multiples: -3.03, peer_chunk_token_num: 15\n", "2025-03-02 17:32:59,931 INFO yield speech index:8, len 0.60, rtf 2.444,  cost 1.466s,  all cost time 8.338s\n", "2025-03-02 17:32:59,932 DEBUG multiples: -3.13, peer_chunk_token_num: 15\n", "2025-03-02 17:32:59,953 INFO yield speech index:8, len 0.60, rtf 2.201,  cost 1.320s,  all cost time 8.431s\n", "2025-03-02 17:32:59,954 DEBUG multiples: -3.14, peer_chunk_token_num: 15\n", "2025-03-02 17:33:00,008 INFO yield speech index:8, len 0.60, rtf 1.856,  cost 1.114s,  all cost time 8.498s\n", "2025-03-02 17:33:00,009 DEBUG multiples: -3.18, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：9\n", "任务 4 进度：9\n", "任务 1 进度：9\n", "任务 0 进度：9\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:00,054 INFO yield speech index:8, len 0.60, rtf 2.013,  cost 1.208s,  all cost time 8.449s\n", "2025-03-02 17:33:00,055 DEBUG multiples: -3.21, peer_chunk_token_num: 15\n", "2025-03-02 17:33:00,092 INFO yield speech index:8, len 0.60, rtf 2.393,  cost 1.436s,  all cost time 8.443s\n", "2025-03-02 17:33:00,093 DEBUG multiples: -3.21, peer_chunk_token_num: 15\n", "2025-03-02 17:33:00,122 INFO yield speech index:8, len 0.60, rtf 1.869,  cost 1.121s,  all cost time 8.548s\n", "2025-03-02 17:33:00,122 DEBUG multiples: -3.26, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 5 进度：9\n", "任务 9 进度：9\n", "任务 3 进度：9\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:00,337 INFO yield speech index:8, len 0.60, rtf 2.037,  cost 1.222s,  all cost time 8.703s\n", "2025-03-02 17:33:00,340 DEBUG multiples: -3.38, peer_chunk_token_num: 15\n", "2025-03-02 17:33:00,354 INFO yield speech index:8, len 0.60, rtf 2.057,  cost 1.235s,  all cost time 8.739s\n", "2025-03-02 17:33:00,357 DEBUG multiples: -3.41, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 8 进度：9\n", "任务 6 进度：9\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:00,614 INFO yield speech index:8, len 0.60, rtf 2.054,  cost 1.232s,  all cost time 9.072s\n", "2025-03-02 17:33:00,616 DEBUG multiples: -3.58, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 2 进度：9\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:01,105 INFO yield speech index:9, len 0.60, rtf 2.160,  cost 1.296s,  all cost time 9.481s\n", "2025-03-02 17:33:01,106 DEBUG multiples: -3.64, peer_chunk_token_num: 15\n", "2025-03-02 17:33:01,155 INFO yield speech index:9, len 0.60, rtf 2.039,  cost 1.224s,  all cost time 9.562s\n", "2025-03-02 17:33:01,158 DEBUG multiples: -3.69, peer_chunk_token_num: 15\n", "2025-03-02 17:33:01,211 INFO yield speech index:9, len 0.60, rtf 2.095,  cost 1.257s,  all cost time 9.689s\n", "2025-03-02 17:33:01,213 DEBUG multiples: -3.72, peer_chunk_token_num: 15\n", "2025-03-02 17:33:01,281 INFO yield speech index:9, len 0.60, rtf 2.120,  cost 1.272s,  all cost time 9.770s\n", "2025-03-02 17:33:01,282 DEBUG multiples: -3.77, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：10\n", "任务 4 进度：10\n", "任务 1 进度：10\n", "任务 0 进度：10\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:01,473 INFO yield speech index:9, len 0.60, rtf 2.364,  cost 1.419s,  all cost time 9.868s\n", "2025-03-02 17:33:01,475 DEBUG multiples: -3.89, peer_chunk_token_num: 15\n", "2025-03-02 17:33:01,479 INFO yield speech index:9, len 0.60, rtf 2.311,  cost 1.387s,  all cost time 9.830s\n", "2025-03-02 17:33:01,480 DEBUG multiples: -3.86, peer_chunk_token_num: 15\n", "2025-03-02 17:33:01,514 INFO yield speech index:9, len 0.60, rtf 2.319,  cost 1.391s,  all cost time 9.940s\n", "2025-03-02 17:33:01,514 DEBUG multiples: -3.91, peer_chunk_token_num: 15\n", "2025-03-02 17:33:01,602 INFO yield speech index:9, len 0.60, rtf 2.104,  cost 1.263s,  all cost time 9.968s\n", "2025-03-02 17:33:01,603 DEBUG multiples: -3.95, peer_chunk_token_num: 15\n", "2025-03-02 17:33:01,633 INFO yield speech index:9, len 0.60, rtf 2.128,  cost 1.277s,  all cost time 10.018s\n", "2025-03-02 17:33:01,634 DEBUG multiples: -3.98, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 5 进度：10\n", "任务 9 进度：10\n", "任务 3 进度：10\n", "任务 8 进度：10\n", "任务 6 进度：10\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:01,802 INFO yield speech index:9, len 0.60, rtf 1.976,  cost 1.186s,  all cost time 10.260s\n", "2025-03-02 17:33:01,804 DEBUG multiples: -4.09, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 2 进度：10\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:02,363 INFO yield speech index:10, len 0.60, rtf 2.095,  cost 1.257s,  all cost time 10.739s\n", "2025-03-02 17:33:02,366 DEBUG multiples: -4.21, peer_chunk_token_num: 15\n", "2025-03-02 17:33:02,481 INFO yield speech index:10, len 0.60, rtf 2.205,  cost 1.323s,  all cost time 10.888s\n", "2025-03-02 17:33:02,484 DEBUG multiples: -4.30, peer_chunk_token_num: 15\n", "2025-03-02 17:33:02,524 INFO yield speech index:10, len 0.60, rtf 2.185,  cost 1.311s,  all cost time 11.002s\n", "2025-03-02 17:33:02,526 DEBUG multiples: -4.32, peer_chunk_token_num: 15\n", "2025-03-02 17:33:02,540 INFO yield speech index:10, len 0.60, rtf 2.096,  cost 1.257s,  all cost time 11.029s\n", "2025-03-02 17:33:02,541 DEBUG multiples: -4.33, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：11\n", "任务 4 进度：11\n", "任务 1 进度：11\n", "任务 0 进度：11\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:02,596 INFO yield speech index:10, len 0.60, rtf 1.860,  cost 1.116s,  all cost time 10.947s\n", "2025-03-02 17:33:02,597 DEBUG multiples: -4.34, peer_chunk_token_num: 15\n", "2025-03-02 17:33:02,600 INFO yield speech index:10, len 0.60, rtf 1.876,  cost 1.126s,  all cost time 10.995s\n", "2025-03-02 17:33:02,601 DEBUG multiples: -4.37, peer_chunk_token_num: 15\n", "2025-03-02 17:33:02,643 INFO yield speech index:10, len 0.60, rtf 1.881,  cost 1.129s,  all cost time 11.069s\n", "2025-03-02 17:33:02,644 DEBUG multiples: -4.39, peer_chunk_token_num: 15\n", "2025-03-02 17:33:02,748 INFO yield speech index:10, len 0.60, rtf 1.908,  cost 1.145s,  all cost time 11.113s\n", "2025-03-02 17:33:02,751 DEBUG multiples: -4.44, peer_chunk_token_num: 15\n", "2025-03-02 17:33:02,768 INFO yield speech index:10, len 0.60, rtf 1.889,  cost 1.134s,  all cost time 11.153s\n", "2025-03-02 17:33:02,770 DEBUG multiples: -4.46, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 9 进度：11\n", "任务 5 进度：11\n", "任务 3 进度：11\n", "任务 8 进度：11\n", "任务 6 进度：11\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:03,061 INFO yield speech index:10, len 0.60, rtf 2.095,  cost 1.257s,  all cost time 11.519s\n", "2025-03-02 17:33:03,063 DEBUG multiples: -4.64, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 2 进度：11\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:03,645 INFO yield speech index:11, len 0.60, rtf 2.132,  cost 1.279s,  all cost time 12.022s\n", "2025-03-02 17:33:03,646 DEBUG multiples: -4.78, peer_chunk_token_num: 15\n", "2025-03-02 17:33:03,715 INFO yield speech index:11, len 0.60, rtf 2.052,  cost 1.231s,  all cost time 12.122s\n", "2025-03-02 17:33:03,717 DEBUG multiples: -4.84, peer_chunk_token_num: 15\n", "2025-03-02 17:33:03,754 INFO yield speech index:11, len 0.60, rtf 2.047,  cost 1.228s,  all cost time 12.232s\n", "2025-03-02 17:33:03,755 DEBUG multiples: -4.86, peer_chunk_token_num: 15\n", "2025-03-02 17:33:03,759 INFO yield speech index:11, len 0.60, rtf 2.031,  cost 1.219s,  all cost time 12.249s\n", "2025-03-02 17:33:03,760 DEBUG multiples: -4.86, peer_chunk_token_num: 15\n", "2025-03-02 17:33:03,838 INFO yield speech index:11, len 0.60, rtf 2.062,  cost 1.237s,  all cost time 12.233s\n", "2025-03-02 17:33:03,839 DEBUG multiples: -4.91, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：12\n", "任务 4 进度：12\n", "任务 1 进度：12\n", "任务 0 进度：12\n", "任务 5 进度：12\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:03,853 INFO yield speech index:11, len 0.60, rtf 2.015,  cost 1.209s,  all cost time 12.279s\n", "2025-03-02 17:33:03,853 DEBUG multiples: -4.92, peer_chunk_token_num: 15\n", "2025-03-02 17:33:03,907 INFO yield speech index:11, len 0.60, rtf 2.183,  cost 1.310s,  all cost time 12.258s\n", "2025-03-02 17:33:03,908 DEBUG multiples: -4.92, peer_chunk_token_num: 15\n", "2025-03-02 17:33:03,913 INFO yield speech index:11, len 0.60, rtf 1.937,  cost 1.162s,  all cost time 12.279s\n", "2025-03-02 17:33:03,914 DEBUG multiples: -4.93, peer_chunk_token_num: 15\n", "2025-03-02 17:33:04,013 INFO yield speech index:11, len 0.60, rtf 2.071,  cost 1.243s,  all cost time 12.398s\n", "2025-03-02 17:33:04,015 DEBUG multiples: -5.00, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 3 进度：12\n", "任务 9 进度：12\n", "任务 8 进度：12\n", "任务 6 进度：12\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:04,167 INFO yield speech index:11, len 0.60, rtf 1.840,  cost 1.104s,  all cost time 12.626s\n", "2025-03-02 17:33:04,168 DEBUG multiples: -5.09, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 2 进度：12\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:04,842 INFO yield speech index:12, len 0.60, rtf 1.993,  cost 1.196s,  all cost time 13.219s\n", "2025-03-02 17:33:04,846 DEBUG multiples: -5.30, peer_chunk_token_num: 15\n", "2025-03-02 17:33:04,903 INFO yield speech index:12, len 0.60, rtf 1.978,  cost 1.187s,  all cost time 13.310s\n", "2025-03-02 17:33:04,905 DEBUG multiples: -5.35, peer_chunk_token_num: 15\n", "2025-03-02 17:33:04,988 INFO yield speech index:12, len 0.60, rtf 2.055,  cost 1.233s,  all cost time 13.465s\n", "2025-03-02 17:33:04,990 DEBUG multiples: -5.40, peer_chunk_token_num: 15\n", "2025-03-02 17:33:05,006 INFO yield speech index:12, len 0.60, rtf 2.076,  cost 1.246s,  all cost time 13.495s\n", "2025-03-02 17:33:05,007 DEBUG multiples: -5.41, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：13\n", "任务 4 进度：13\n", "任务 1 进度：13\n", "任务 0 进度：13\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:05,067 INFO yield speech index:12, len 0.60, rtf 2.047,  cost 1.228s,  all cost time 13.462s\n", "2025-03-02 17:33:05,071 DEBUG multiples: -5.44, peer_chunk_token_num: 15\n", "2025-03-02 17:33:05,096 INFO yield speech index:12, len 0.60, rtf 2.071,  cost 1.243s,  all cost time 13.522s\n", "2025-03-02 17:33:05,100 DEBUG multiples: -5.46, peer_chunk_token_num: 15\n", "2025-03-02 17:33:05,179 INFO yield speech index:12, len 0.60, rtf 2.120,  cost 1.272s,  all cost time 13.530s\n", "2025-03-02 17:33:05,181 DEBUG multiples: -5.48, peer_chunk_token_num: 15\n", "2025-03-02 17:33:05,185 INFO yield speech index:12, len 0.60, rtf 2.118,  cost 1.271s,  all cost time 13.550s\n", "2025-03-02 17:33:05,185 DEBUG multiples: -5.49, peer_chunk_token_num: 15\n", "2025-03-02 17:33:05,261 INFO yield speech index:12, len 0.60, rtf 2.076,  cost 1.246s,  all cost time 13.646s\n", "2025-03-02 17:33:05,262 DEBUG multiples: -5.54, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 5 进度：13\n", "任务 3 进度：13\n", "任务 9 进度：13\n", "任务 8 进度：13\n", "任务 6 进度：13\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:05,414 INFO yield speech index:12, len 0.60, rtf 2.077,  cost 1.246s,  all cost time 13.873s\n", "2025-03-02 17:33:05,415 DEBUG multiples: -5.63, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 2 进度：13\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:06,088 INFO yield speech index:13, len 0.60, rtf 2.071,  cost 1.243s,  all cost time 14.465s\n", "2025-03-02 17:33:06,089 DEBUG multiples: -5.84, peer_chunk_token_num: 15\n", "2025-03-02 17:33:06,169 INFO yield speech index:13, len 0.60, rtf 2.107,  cost 1.264s,  all cost time 14.576s\n", "2025-03-02 17:33:06,170 DEBUG multiples: -5.90, peer_chunk_token_num: 15\n", "2025-03-02 17:33:06,219 INFO yield speech index:13, len 0.60, rtf 2.049,  cost 1.229s,  all cost time 14.697s\n", "2025-03-02 17:33:06,220 DEBUG multiples: -5.93, peer_chunk_token_num: 15\n", "2025-03-02 17:33:06,242 INFO yield speech index:13, len 0.60, rtf 2.058,  cost 1.235s,  all cost time 14.732s\n", "2025-03-02 17:33:06,243 DEBUG multiples: -5.94, peer_chunk_token_num: 15\n", "2025-03-02 17:33:06,279 INFO yield speech index:13, len 0.60, rtf 2.014,  cost 1.208s,  all cost time 14.674s\n", "2025-03-02 17:33:06,280 DEBUG multiples: -5.96, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：14\n", "任务 4 进度：14\n", "任务 1 进度：14\n", "任务 0 进度：14\n", "任务 5 进度：14\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:06,304 INFO yield speech index:13, len 0.60, rtf 2.008,  cost 1.205s,  all cost time 14.730s\n", "2025-03-02 17:33:06,306 DEBUG multiples: -5.97, peer_chunk_token_num: 15\n", "2025-03-02 17:33:06,341 INFO yield speech index:13, len 0.60, rtf 1.926,  cost 1.155s,  all cost time 14.707s\n", "2025-03-02 17:33:06,342 DEBUG multiples: -5.98, peer_chunk_token_num: 15\n", "2025-03-02 17:33:06,342 INFO yield speech index:13, len 0.60, rtf 1.936,  cost 1.162s,  all cost time 14.693s\n", "2025-03-02 17:33:06,343 DEBUG multiples: -5.97, peer_chunk_token_num: 15\n", "2025-03-02 17:33:06,474 INFO yield speech index:13, len 0.60, rtf 2.020,  cost 1.212s,  all cost time 14.859s\n", "2025-03-02 17:33:06,479 DEBUG multiples: -6.06, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 3 进度：14\n", "任务 8 进度：14\n", "任务 9 进度：14\n", "任务 6 进度：14\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:06,730 INFO yield speech index:13, len 0.60, rtf 2.191,  cost 1.315s,  all cost time 15.188s\n", "2025-03-02 17:33:06,731 DEBUG multiples: -6.20, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 2 进度：14\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:07,488 INFO yield speech index:14, len 0.60, rtf 2.331,  cost 1.399s,  all cost time 15.864s\n", "2025-03-02 17:33:07,492 DEBUG multiples: -6.47, peer_chunk_token_num: 15\n", "2025-03-02 17:33:07,601 INFO yield speech index:14, len 0.60, rtf 2.386,  cost 1.431s,  all cost time 16.008s\n", "2025-03-02 17:33:07,604 DEBUG multiples: -6.54, peer_chunk_token_num: 15\n", "2025-03-02 17:33:07,665 INFO yield speech index:14, len 0.60, rtf 2.409,  cost 1.445s,  all cost time 16.143s\n", "2025-03-02 17:33:07,666 DEBUG multiples: -6.57, peer_chunk_token_num: 15\n", "2025-03-02 17:33:07,677 INFO yield speech index:14, len 0.60, rtf 2.391,  cost 1.434s,  all cost time 16.167s\n", "2025-03-02 17:33:07,678 DEBUG multiples: -6.57, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：15\n", "任务 4 进度：15\n", "任务 1 进度：15\n", "任务 0 进度：15\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:07,718 INFO yield speech index:14, len 0.60, rtf 2.397,  cost 1.438s,  all cost time 16.113s\n", "2025-03-02 17:33:07,722 DEBUG multiples: -6.60, peer_chunk_token_num: 15\n", "2025-03-02 17:33:07,725 INFO yield speech index:14, len 0.60, rtf 2.365,  cost 1.419s,  all cost time 16.151s\n", "2025-03-02 17:33:07,728 DEBUG multiples: -6.60, peer_chunk_token_num: 15\n", "2025-03-02 17:33:07,754 INFO yield speech index:14, len 0.60, rtf 2.355,  cost 1.413s,  all cost time 16.120s\n", "2025-03-02 17:33:07,756 DEBUG multiples: -6.60, peer_chunk_token_num: 15\n", "2025-03-02 17:33:07,768 INFO yield speech index:14, len 0.60, rtf 2.375,  cost 1.425s,  all cost time 16.118s\n", "2025-03-02 17:33:07,769 DEBUG multiples: -6.60, peer_chunk_token_num: 15\n", "2025-03-02 17:33:07,813 INFO yield speech index:14, len 0.60, rtf 2.223,  cost 1.334s,  all cost time 16.198s\n", "2025-03-02 17:33:07,814 DEBUG multiples: -6.64, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 5 进度：15\n", "任务 3 进度：15\n", "任务 8 进度：15\n", "任务 9 进度：15\n", "任务 6 进度：15\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:07,965 INFO yield speech index:14, len 0.60, rtf 2.056,  cost 1.234s,  all cost time 16.423s\n", "2025-03-02 17:33:07,965 DEBUG multiples: -6.72, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 2 进度：15\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:08,793 INFO yield speech index:15, len 0.60, rtf 2.169,  cost 1.301s,  all cost time 17.169s\n", "2025-03-02 17:33:08,795 DEBUG multiples: -7.03, peer_chunk_token_num: 15\n", "2025-03-02 17:33:08,916 INFO yield speech index:15, len 0.60, rtf 2.186,  cost 1.312s,  all cost time 17.323s\n", "2025-03-02 17:33:08,917 DEBUG multiples: -7.10, peer_chunk_token_num: 15\n", "2025-03-02 17:33:08,954 INFO yield speech index:15, len 0.60, rtf 2.147,  cost 1.288s,  all cost time 17.432s\n", "2025-03-02 17:33:08,955 DEBUG multiples: -7.12, peer_chunk_token_num: 15\n", "2025-03-02 17:33:08,965 INFO yield speech index:15, len 0.60, rtf 2.145,  cost 1.287s,  all cost time 17.455s\n", "2025-03-02 17:33:08,967 DEBUG multiples: -7.13, peer_chunk_token_num: 15\n", "2025-03-02 17:33:08,986 INFO yield speech index:15, len 0.60, rtf 2.106,  cost 1.264s,  all cost time 17.381s\n", "2025-03-02 17:33:08,987 DEBUG multiples: -7.14, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：16\n", "任务 4 进度：16\n", "任务 1 进度：16\n", "任务 0 进度：16\n", "任务 5 进度：16\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:09,029 INFO yield speech index:15, len 0.60, rtf 2.168,  cost 1.301s,  all cost time 17.455s\n", "2025-03-02 17:33:09,031 DEBUG multiples: -7.16, peer_chunk_token_num: 15\n", "2025-03-02 17:33:09,033 INFO yield speech index:15, len 0.60, rtf 2.108,  cost 1.265s,  all cost time 17.384s\n", "2025-03-02 17:33:09,034 DEBUG multiples: -7.14, peer_chunk_token_num: 15\n", "2025-03-02 17:33:09,035 INFO yield speech index:15, len 0.60, rtf 2.131,  cost 1.279s,  all cost time 17.401s\n", "2025-03-02 17:33:09,036 DEBUG multiples: -7.15, peer_chunk_token_num: 15\n", "2025-03-02 17:33:09,074 INFO yield speech index:15, len 0.60, rtf 2.101,  cost 1.260s,  all cost time 17.459s\n", "2025-03-02 17:33:09,075 DEBUG multiples: -7.18, peer_chunk_token_num: 15\n", "2025-03-02 17:33:09,174 INFO yield speech index:15, len 0.60, rtf 2.015,  cost 1.209s,  all cost time 17.633s\n", "2025-03-02 17:33:09,177 DEBUG multiples: -7.23, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 3 进度：16\n", "任务 9 进度：16\n", "任务 8 进度：16\n", "任务 6 进度：16\n", "任务 2 进度：16\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:10,458 INFO yield speech index:16, len 0.60, rtf 2.771,  cost 1.663s,  all cost time 18.835s\n", "2025-03-02 17:33:10,459 DEBUG multiples: -7.77, peer_chunk_token_num: 15\n", "2025-03-02 17:33:10,500 INFO yield speech index:16, len 0.60, rtf 2.638,  cost 1.583s,  all cost time 18.907s\n", "2025-03-02 17:33:10,500 DEBUG multiples: -7.80, peer_chunk_token_num: 15\n", "2025-03-02 17:33:10,584 INFO yield speech index:16, len 0.60, rtf 2.715,  cost 1.629s,  all cost time 19.062s\n", "2025-03-02 17:33:10,585 DEBUG multiples: -7.84, peer_chunk_token_num: 15\n", "2025-03-02 17:33:10,599 INFO yield speech index:16, len 0.60, rtf 2.721,  cost 1.632s,  all cost time 19.089s\n", "2025-03-02 17:33:10,600 DEBUG multiples: -7.85, peer_chunk_token_num: 15\n", "2025-03-02 17:33:10,633 INFO yield speech index:16, len 0.60, rtf 2.743,  cost 1.646s,  all cost time 19.027s\n", "2025-03-02 17:33:10,634 DEBUG multiples: -7.86, peer_chunk_token_num: 15\n", "2025-03-02 17:33:10,635 INFO yield speech index:16, len 0.60, rtf 2.665,  cost 1.599s,  all cost time 19.000s\n", "2025-03-02 17:33:10,635 DEBUG multiples: -7.85, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：17\n", "任务 4 进度：17\n", "任务 1 进度：17\n", "任务 0 进度：17\n", "任务 5 进度：17\n", "任务 8 进度：17\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:10,708 INFO yield speech index:16, len 0.60, rtf 2.795,  cost 1.677s,  all cost time 19.135s\n", "2025-03-02 17:33:10,709 DEBUG multiples: -7.90, peer_chunk_token_num: 15\n", "2025-03-02 17:33:10,711 INFO yield speech index:16, len 0.80, rtf 2.096,  cost 1.677s,  all cost time 19.062s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:19<00:00, 19.06s/it]\n", "2025-03-02 17:33:10,759 INFO yield speech index:16, len 0.60, rtf 2.806,  cost 1.684s,  all cost time 19.144s\n", "2025-03-02 17:33:10,759 DEBUG multiples: -7.92, peer_chunk_token_num: 15\n", "2025-03-02 17:33:10,825 INFO yield speech index:16, len 0.60, rtf 2.747,  cost 1.648s,  all cost time 19.283s\n", "2025-03-02 17:33:10,825 DEBUG multiples: -7.95, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 3 进度：17\n", "任务 9 进度：17\n", "任务 9 完成，生成 17 个片段\n", "任务 6 进度：17\n", "任务 2 进度：17\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:11,724 INFO yield speech index:17, len 0.60, rtf 2.108,  cost 1.265s,  all cost time 20.100s\n", "2025-03-02 17:33:11,726 DEBUG multiples: -8.31, peer_chunk_token_num: 15\n", "2025-03-02 17:33:11,869 INFO yield speech index:17, len 0.60, rtf 2.281,  cost 1.368s,  all cost time 20.276s\n", "2025-03-02 17:33:11,869 DEBUG multiples: -8.38, peer_chunk_token_num: 15\n", "2025-03-02 17:33:11,881 INFO yield speech index:17, len 0.60, rtf 2.135,  cost 1.281s,  all cost time 20.371s\n", "2025-03-02 17:33:11,882 DEBUG multiples: -8.39, peer_chunk_token_num: 15\n", "2025-03-02 17:33:11,918 INFO yield speech index:17, len 0.60, rtf 2.221,  cost 1.333s,  all cost time 20.395s\n", "2025-03-02 17:33:11,918 DEBUG multiples: -8.41, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：18\n", "任务 4 进度：18\n", "任务 0 进度：18\n", "任务 1 进度：18\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:11,987 INFO yield speech index:17, len 0.60, rtf 2.254,  cost 1.352s,  all cost time 20.353s\n", "2025-03-02 17:33:11,988 DEBUG multiples: -8.42, peer_chunk_token_num: 15\n", "2025-03-02 17:33:12,031 INFO yield speech index:17, len 0.60, rtf 2.328,  cost 1.397s,  all cost time 20.425s\n", "2025-03-02 17:33:12,031 DEBUG multiples: -8.46, peer_chunk_token_num: 15\n", "2025-03-02 17:33:12,038 INFO yield speech index:17, len 0.60, rtf 2.214,  cost 1.329s,  all cost time 20.464s\n", "2025-03-02 17:33:12,039 DEBUG multiples: -8.46, peer_chunk_token_num: 15\n", "2025-03-02 17:33:12,097 INFO yield speech index:17, len 0.60, rtf 2.230,  cost 1.338s,  all cost time 20.483s\n", "2025-03-02 17:33:12,098 DEBUG multiples: -8.49, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 8 进度：18\n", "任务 5 进度：18\n", "任务 3 进度：18\n", "任务 6 进度：18\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:12,198 INFO yield speech index:17, len 0.68, rtf 2.018,  cost 1.372s,  all cost time 20.656s\n", "\n", "100%|██████████| 1/1 [00:20<00:00, 20.66s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 2 进度：18\n", "任务 2 完成，生成 18 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:12,963 INFO yield speech index:18, len 0.60, rtf 2.063,  cost 1.238s,  all cost time 21.340s\n", "2025-03-02 17:33:12,964 DEBUG multiples: -8.83, peer_chunk_token_num: 15\n", "2025-03-02 17:33:13,053 INFO yield speech index:18, len 0.60, rtf 1.972,  cost 1.183s,  all cost time 21.460s\n", "2025-03-02 17:33:13,054 DEBUG multiples: -8.88, peer_chunk_token_num: 15\n", "2025-03-02 17:33:13,099 INFO yield speech index:18, len 0.60, rtf 2.029,  cost 1.217s,  all cost time 21.589s\n", "2025-03-02 17:33:13,100 DEBUG multiples: -8.90, peer_chunk_token_num: 15\n", "2025-03-02 17:33:13,134 INFO yield speech index:18, len 0.60, rtf 2.026,  cost 1.216s,  all cost time 21.611s\n", "2025-03-02 17:33:13,136 DEBUG multiples: -8.92, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：19\n", "任务 4 进度：19\n", "任务 0 进度：19\n", "任务 1 进度：19\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:13,257 INFO yield speech index:18, len 0.60, rtf 2.116,  cost 1.270s,  all cost time 21.623s\n", "2025-03-02 17:33:13,259 DEBUG multiples: -8.96, peer_chunk_token_num: 15\n", "2025-03-02 17:33:13,261 INFO yield speech index:18, len 0.60, rtf 2.049,  cost 1.229s,  all cost time 21.655s\n", "2025-03-02 17:33:13,261 DEBUG multiples: -8.97, peer_chunk_token_num: 15\n", "2025-03-02 17:33:13,262 INFO yield speech index:18, len 0.60, rtf 2.039,  cost 1.224s,  all cost time 21.689s\n", "2025-03-02 17:33:13,263 DEBUG multiples: -8.98, peer_chunk_token_num: 15\n", "2025-03-02 17:33:13,329 INFO yield speech index:18, len 0.60, rtf 2.052,  cost 1.231s,  all cost time 21.714s\n", "2025-03-02 17:33:13,330 DEBUG multiples: -9.00, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 8 进度：19\n", "任务 5 进度：19\n", "任务 3 进度：19\n", "任务 6 进度：19\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:14,777 INFO yield speech index:19, len 0.60, rtf 3.022,  cost 1.813s,  all cost time 23.154s\n", "2025-03-02 17:33:14,780 DEBUG multiples: -9.61, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：20\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:15,026 INFO yield speech index:19, len 0.60, rtf 3.210,  cost 1.926s,  all cost time 23.515s\n", "2025-03-02 17:33:15,028 DEBUG multiples: -9.73, peer_chunk_token_num: 15\n", "2025-03-02 17:33:15,031 INFO yield speech index:19, len 0.60, rtf 3.294,  cost 1.977s,  all cost time 23.437s\n", "2025-03-02 17:33:15,032 DEBUG multiples: -9.73, peer_chunk_token_num: 15\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 0 进度：20\n", "任务 4 进度：20\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:15,387 INFO yield speech index:19, len 0.68, rtf 3.310,  cost 2.251s,  all cost time 23.864s\n", "100%|██████████| 1/1 [00:23<00:00, 23.87s/it]\n", "2025-03-02 17:33:15,427 INFO yield speech index:19, len 0.60, rtf 3.613,  cost 2.168s,  all cost time 23.792s\n", "2025-03-02 17:33:15,430 DEBUG multiples: -9.89, peer_chunk_token_num: 15\n", "2025-03-02 17:33:15,439 INFO yield speech index:19, len 0.60, rtf 3.629,  cost 2.177s,  all cost time 23.833s\n", "2025-03-02 17:33:15,441 DEBUG multiples: -9.91, peer_chunk_token_num: 15\n", "2025-03-02 17:33:15,575 INFO yield speech index:19, len 0.60, rtf 3.742,  cost 2.245s,  all cost time 23.960s\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:23<00:00, 23.97s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 1 进度：20\n", "任务 1 完成，生成 20 个片段\n", "任务 8 进度：20\n", "任务 5 进度：20\n", "任务 6 进度：20\n", "任务 6 完成，生成 20 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:15,692 INFO yield speech index:19, len 0.56, rtf 4.338,  cost 2.429s,  all cost time 24.118s\n", "\n", "\n", "100%|██████████| 1/1 [00:24<00:00, 24.12s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 3 进度：20\n", "任务 3 完成，生成 20 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:16,910 INFO yield speech index:20, len 0.60, rtf 3.551,  cost 2.130s,  all cost time 25.287s\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:25<00:00, 25.29s/it]\n", "2025-03-02 17:33:17,099 INFO yield speech index:20, len 0.80, rtf 2.588,  cost 2.071s,  all cost time 25.588s\n", "100%|██████████| 1/1 [00:25<00:00, 25.59s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 7 进度：21\n", "任务 7 完成，生成 21 个片段\n", "任务 0 进度：21\n", "任务 0 完成，生成 21 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:17,145 INFO yield speech index:20, len 0.52, rtf 4.062,  cost 2.112s,  all cost time 25.552s\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:25<00:00, 25.56s/it]\n", "2025-03-02 17:33:17,199 INFO yield speech index:20, len 0.60, rtf 2.949,  cost 1.770s,  all cost time 25.565s\n", "2025-03-02 17:33:17,202 DEBUG multiples: -10.63, peer_chunk_token_num: 15\n", "2025-03-02 17:33:17,329 INFO yield speech index:20, len 0.32, rtf 5.900,  cost 1.888s,  all cost time 25.724s\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:25<00:00, 25.73s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 4 进度：21\n", "任务 4 完成，生成 21 个片段\n", "任务 8 进度：21\n", "任务 5 进度：21\n", "任务 5 完成，生成 21 个片段\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-03-02 17:33:17,456 INFO yield speech index:21, len 0.36, rtf 0.708,  cost 0.255s,  all cost time 25.822s\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "100%|██████████| 1/1 [00:25<00:00, 25.82s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["任务 8 进度：22\n", "任务 8 完成，生成 22 个片段\n", "--- 25.962302207946777 seconds ---\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["start_time = time.time()\n", "await test_concurrent_instruct(10, semaphore_limit=10, stream=True)\n", "print(\"--- %s seconds ---\" % (time.time() - start_time))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 新增 spk_info 方法"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from cosyvoice.utils.file_utils import load_wav\n", "\n", "prompt_text = '希望你以后能够做的比我还好呦'\n", "prompt_speech_16k = load_wav('/home/<USER>/音乐/希望你以后能够做的比我还好呦.wav', 16000)\n", "cosyvoice.frontend.generate_spk_info(\n", "    '001',\n", "    prompt_text=prompt_text,\n", "    prompt_speech_16k=prompt_speech_16k,\n", "    name='系统默认'\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from cosyvoice.utils.file_utils import load_wav\n", "\n", "prompt_text = '以温润磁性的声线，宛如夏日细雨。'\n", "prompt_speech_16k = load_wav('/home/<USER>/音乐/（龙小夏）以温润磁性的声线，宛如夏日细雨.wav', 16000)\n", "cosyvoice.frontend.generate_spk_info(\n", "    'longxiaoxia',\n", "    prompt_text=prompt_text,\n", "    prompt_speech_16k=prompt_speech_16k,\n", "    name='龙小夏'\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from cosyvoice.utils.file_utils import load_wav\n", "\n", "prompt_text = '今天天气真是太好了，阳光灿烂心情超级棒'\n", "prompt_speech_16k = load_wav('/home/<USER>/音乐/(湾湾小何)今天天气真是太好了，阳光灿烂心情超级棒.wav', 16000)\n", "cosyvoice.frontend.generate_spk_info(\n", "    'xiaohe',\n", "    prompt_text=prompt_text,\n", "    prompt_speech_16k=prompt_speech_16k,\n", "    name='湾湾小何'\n", ")"]}], "metadata": {"kernelspec": {"display_name": "cosyvoice2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}